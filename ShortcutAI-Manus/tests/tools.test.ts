/**
 * Tools Tests - Test all tool implementations
 */

import { ToolManager } from '../src/tools';
import { ExecuteExcelCodeTool } from '../src/tools/executeExcelCode';
import { WebSearchTool } from '../src/tools/webSearch';
import { FilePerceptionTool } from '../src/tools/filePerception';
import { EdgarSearchTool } from '../src/tools/edgarSearch';

describe('ToolManager', () => {
  let toolManager: ToolManager;

  beforeEach(async () => {
    toolManager = new ToolManager();
    await toolManager.initialize();
  });

  describe('Initialization', () => {
    test('should initialize with all tools', async () => {
      const tools = toolManager.getAvailableTools();
      
      expect(tools).toContain('executeExcelCode');
      expect(tools).toContain('webSearch');
      expect(tools).toContain('filePerception');
      expect(tools).toContain('edgarSearch');
      expect(tools).toContain('todoList');
      expect(tools).toContain('attributeReferences');
      expect(tools).toContain('readOnlyAnalysis');
      expect(tools).toContain('clarifyWithUser');
    });

    test('should provide tool descriptions', () => {
      const description = toolManager.getToolDescription('executeExcelCode');
      expect(description).toBeDefined();
      expect(description).toContain('spreadsheet');
    });
  });

  describe('Tool Execution', () => {
    test('should execute tools successfully', async () => {
      const result = await toolManager.executeTool('executeExcelCode', {
        code: 'return "Hello World";',
      });

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
    });

    test('should handle invalid tool names', async () => {
      await expect(
        toolManager.executeTool('invalidTool', {})
      ).rejects.toThrow('Tool not found');
    });

    test('should validate parameters', async () => {
      await expect(
        toolManager.executeTool('executeExcelCode', {})
      ).rejects.toThrow('Invalid parameters');
    });
  });

  describe('Convenience Methods', () => {
    test('should execute Excel code via convenience method', async () => {
      const result = await toolManager.executeExcelCode('return 42;');
      expect(result).toBeDefined();
    });

    test('should perform web search via convenience method', async () => {
      const result = await toolManager.webSearch('test query');
      expect(result).toBeDefined();
      expect(result.success).toBe(true);
    });

    test('should analyze files via convenience method', async () => {
      const mockFile = Buffer.from('test content');
      const result = await toolManager.analyzeFile(mockFile);
      expect(result).toBeDefined();
    });
  });
});

describe('ExecuteExcelCodeTool', () => {
  let tool: ExecuteExcelCodeTool;

  beforeEach(() => {
    tool = new ExecuteExcelCodeTool();
  });

  describe('Parameter Validation', () => {
    test('should validate required parameters', () => {
      expect(tool.validate({ code: 'test code' })).toBe(true);
      expect(tool.validate({})).toBe(false);
      expect(tool.validate({ code: '' })).toBe(false);
    });

    test('should validate parameter types', () => {
      expect(tool.validate({ code: 123 })).toBe(false);
      expect(tool.validate(null)).toBe(false);
    });
  });

  describe('Code Execution', () => {
    test('should execute simple code', async () => {
      const result = await tool.execute({
        code: 'return 1 + 1;',
      });

      expect(result.success).toBe(true);
      expect(result.result).toBe(2);
      expect(result.executionTime).toBeGreaterThan(0);
    });

    test('should handle execution errors', async () => {
      const result = await tool.execute({
        code: 'throw new Error("Test error");',
      });

      expect(result.success).toBe(false);
      expect(result.errors).toContain('Test error');
    });

    test('should track execution time', async () => {
      const result = await tool.execute({
        code: 'return "test";',
      });

      expect(result.executionTime).toBeGreaterThan(0);
    });
  });

  describe('Helper Methods', () => {
    test('should set cell values', async () => {
      const result = await tool.setCellValue('A1', 'test value');
      expect(result.success).toBe(true);
    });

    test('should set cell formulas', async () => {
      const result = await tool.setCellFormula('B1', '=A1*2');
      expect(result.success).toBe(true);
    });

    test('should format ranges', async () => {
      const result = await tool.formatRange('A1:B2', { font: { bold: true } });
      expect(result.success).toBe(true);
    });

    test('should import data', async () => {
      const data = [
        ['Name', 'Age', 'City'],
        ['John', 30, 'New York'],
        ['Jane', 25, 'Los Angeles'],
      ];

      const result = await tool.importData(data, 'A1');
      expect(result.success).toBe(true);
      expect(result.result.rowsImported).toBe(3);
    });

    test('should calculate ranges', async () => {
      const result = await tool.calculateRange('A1:A10', 'sum');
      expect(result.success).toBe(true);
      expect(result.result.operation).toBe('sum');
    });
  });
});

describe('WebSearchTool', () => {
  let tool: WebSearchTool;

  beforeEach(() => {
    tool = new WebSearchTool();
  });

  describe('Parameter Validation', () => {
    test('should validate search parameters', () => {
      expect(tool.validate({ query: 'test query' })).toBe(true);
      expect(tool.validate({})).toBe(false);
      expect(tool.validate({ query: '' })).toBe(false);
    });

    test('should validate optional parameters', () => {
      expect(tool.validate({ 
        query: 'test', 
        maxResults: 10,
        searchType: 'financial' 
      })).toBe(true);
      
      expect(tool.validate({ 
        query: 'test', 
        maxResults: 0 
      })).toBe(false);
    });
  });

  describe('Search Execution', () => {
    test('should perform basic search', async () => {
      const result = await tool.execute({
        query: 'financial data',
      });

      expect(result.success).toBe(true);
      expect(result.results).toBeDefined();
      expect(result.results.length).toBeGreaterThan(0);
      expect(result.searchTime).toBeGreaterThan(0);
    });

    test('should handle search types', async () => {
      const result = await tool.execute({
        query: 'stock prices',
        searchType: 'financial',
      });

      expect(result.success).toBe(true);
      expect(result.results[0].extractedData?.type).toBe('financial');
    });

    test('should limit results', async () => {
      const result = await tool.execute({
        query: 'test query',
        maxResults: 1,
      });

      expect(result.results.length).toBeLessThanOrEqual(1);
    });
  });

  describe('Helper Methods', () => {
    test('should search financial data', async () => {
      const result = await tool.searchFinancialData('AAPL', 'quote');
      expect(result.success).toBe(true);
      expect(result.query).toContain('AAPL');
    });

    test('should search company info', async () => {
      const result = await tool.searchCompanyInfo('Apple Inc');
      expect(result.success).toBe(true);
      expect(result.query).toContain('Apple Inc');
    });

    test('should search market data', async () => {
      const result = await tool.searchMarketData('S&P 500');
      expect(result.success).toBe(true);
      expect(result.query).toContain('S&P 500');
    });
  });
});

describe('FilePerceptionTool', () => {
  let tool: FilePerceptionTool;

  beforeEach(() => {
    tool = new FilePerceptionTool();
  });

  describe('Parameter Validation', () => {
    test('should validate file parameters', () => {
      const mockFile = Buffer.from('test content');
      expect(tool.validate({ file: mockFile })).toBe(true);
      expect(tool.validate({})).toBe(false);
    });

    test('should validate optional parameters', () => {
      const mockFile = Buffer.from('test content');
      expect(tool.validate({ 
        file: mockFile, 
        type: 'pdf',
        extractionMode: 'tables' 
      })).toBe(true);
      
      expect(tool.validate({ 
        file: mockFile, 
        type: 'invalid' 
      })).toBe(false);
    });
  });

  describe('File Processing', () => {
    test('should process PDF files', async () => {
      // Create a mock PDF buffer
      const pdfBuffer = Buffer.from('%PDF-1.4\ntest content');
      
      const result = await tool.execute({
        file: pdfBuffer,
        type: 'pdf',
      });

      expect(result.success).toBe(true);
      expect(result.fileType).toBe('pdf');
      expect(result.extractedText).toBeDefined();
    });

    test('should process image files', async () => {
      // Create a mock image buffer
      const imageBuffer = Buffer.from([0xFF, 0xD8, 0xFF, 0xE0]); // JPEG header
      
      const result = await tool.execute({
        file: imageBuffer,
        type: 'image',
      });

      expect(result.success).toBe(true);
      expect(result.fileType).toBe('image');
    });

    test('should extract tables', async () => {
      const mockFile = Buffer.from('test content');
      
      const result = await tool.execute({
        file: mockFile,
        extractionMode: 'tables',
      });

      expect(result.success).toBe(true);
      if (result.tables) {
        expect(result.tables.length).toBeGreaterThanOrEqual(0);
      }
    });
  });

  describe('Helper Methods', () => {
    test('should extract financial data', async () => {
      const mockFile = Buffer.from('financial data');
      const result = await tool.extractFinancialData(mockFile);
      expect(result.success).toBe(true);
    });

    test('should extract form data', async () => {
      const mockFile = Buffer.from('form data');
      const result = await tool.extractFormData(mockFile);
      expect(result.success).toBe(true);
    });

    test('should extract all data', async () => {
      const mockFile = Buffer.from('all data');
      const result = await tool.extractAllData(mockFile);
      expect(result.success).toBe(true);
    });
  });
});

describe('EdgarSearchTool', () => {
  let tool: EdgarSearchTool;

  beforeEach(() => {
    tool = new EdgarSearchTool();
  });

  describe('Parameter Validation', () => {
    test('should validate search parameters', () => {
      expect(tool.validate({ query: 'Apple Inc' })).toBe(true);
      expect(tool.validate({})).toBe(false);
      expect(tool.validate({ query: '' })).toBe(false);
    });

    test('should validate optional parameters', () => {
      expect(tool.validate({ 
        query: 'test', 
        filingType: '10-K',
        maxResults: 10 
      })).toBe(true);
      
      expect(tool.validate({ 
        query: 'test', 
        maxResults: 0 
      })).toBe(false);
    });
  });

  describe('Search Execution', () => {
    test('should search SEC filings', async () => {
      const result = await tool.execute({
        query: 'Apple Inc',
      });

      expect(result.success).toBe(true);
      expect(result.filings).toBeDefined();
      expect(result.searchTime).toBeGreaterThan(0);
    });

    test('should filter by filing type', async () => {
      const result = await tool.execute({
        query: 'test company',
        filingType: '10-K',
      });

      expect(result.success).toBe(true);
      if (result.filings.length > 0) {
        expect(result.filings[0].filingType).toBe('10-K');
      }
    });

    test('should filter by company', async () => {
      const result = await tool.execute({
        query: 'financial data',
        company: 'Example Corp',
      });

      expect(result.success).toBe(true);
      if (result.filings.length > 0) {
        expect(result.filings[0].company).toContain('Example');
      }
    });
  });

  describe('Helper Methods', () => {
    test('should search company filings', async () => {
      const result = await tool.searchCompanyFilings('Apple Inc', '10-K');
      expect(result.success).toBe(true);
      expect(result.query).toContain('Apple Inc');
    });

    test('should search recent filings', async () => {
      const result = await tool.searchRecentFilings('10-Q', 30);
      expect(result.success).toBe(true);
      expect(result.query).toContain('Recent');
    });

    test('should search by CIK', async () => {
      const result = await tool.searchByCIK('0001234567');
      expect(result.success).toBe(true);
      expect(result.query).toContain('CIK');
    });
  });
});
