/**
 * Agent Tests - Test the main agent functionality
 */

import { ShortcutAgent } from '../src/agent/main';
import { TaskClassifier } from '../src/agent/task_classifier';
import { TaskPlanner } from '../src/agent/planner';
import { ResponseGenerator } from '../src/agent/response_generator';

describe('ShortcutAgent', () => {
  let agent: ShortcutAgent;

  beforeEach(async () => {
    agent = new ShortcutAgent({
      maxTaskComplexity: 5,
      enableDebugMode: true,
      sessionTimeout: 60000,
    });
    await agent.initialize();
  });

  afterEach(async () => {
    await agent.shutdown();
  });

  describe('Initialization', () => {
    test('should initialize successfully', async () => {
      const newAgent = new ShortcutAgent();
      await expect(newAgent.initialize()).resolves.not.toThrow();
      await newAgent.shutdown();
    });

    test('should not initialize twice', async () => {
      await expect(agent.initialize()).resolves.not.toThrow();
    });
  });

  describe('Message Processing', () => {
    test('should process chat messages', async () => {
      const message = {
        id: 'test-msg-1',
        content: 'Hello, what can you help me with?',
        timestamp: new Date(),
      };

      const response = await agent.processMessage(message);

      expect(response).toBeDefined();
      expect(response.id).toBeDefined();
      expect(response.content).toContain('spreadsheet');
      expect(response.timestamp).toBeInstanceOf(Date);
    });

    test('should process task messages', async () => {
      const message = {
        id: 'test-msg-2',
        content: 'Create a financial model for a SaaS company',
        timestamp: new Date(),
      };

      const response = await agent.processMessage(message);

      expect(response).toBeDefined();
      expect(response.actions).toBeDefined();
      expect(response.checkpointId).toBeDefined();
    });

    test('should process analysis messages', async () => {
      const message = {
        id: 'test-msg-3',
        content: 'Analyze the formulas in my spreadsheet',
        timestamp: new Date(),
      };

      const response = await agent.processMessage(message);

      expect(response).toBeDefined();
      expect(response.content).toContain('analysis');
    });

    test('should handle file attachments', async () => {
      const mockFile = new File(['test content'], 'test.xlsx', {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });

      const message = {
        id: 'test-msg-4',
        content: 'Analyze this spreadsheet',
        timestamp: new Date(),
        attachments: [mockFile],
      };

      const response = await agent.processMessage(message);

      expect(response).toBeDefined();
      expect(response.content).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    test('should handle invalid messages gracefully', async () => {
      const message = {
        id: 'test-msg-5',
        content: '',
        timestamp: new Date(),
      };

      const response = await agent.processMessage(message);

      expect(response).toBeDefined();
      expect(response.content).toContain('clarify');
    });

    test('should handle processing errors', async () => {
      // Mock a processing error
      const originalProcessMessage = agent.processMessage;
      agent.processMessage = jest.fn().mockRejectedValue(new Error('Test error'));

      const message = {
        id: 'test-msg-6',
        content: 'Test message',
        timestamp: new Date(),
      };

      const response = await originalProcessMessage.call(agent, message);

      expect(response).toBeDefined();
      expect(response.content).toContain('error');
    });
  });
});

describe('TaskClassifier', () => {
  let classifier: TaskClassifier;

  beforeEach(() => {
    classifier = new TaskClassifier();
  });

  describe('Intent Classification', () => {
    test('should classify chat intents', async () => {
      const result = await classifier.classifyIntent('Hello, how are you?');

      expect(result.type).toBe('CHAT');
      expect(result.confidence).toBeGreaterThan(0.5);
      expect(result.context.keywords).toContain('hello');
    });

    test('should classify task intents', async () => {
      const result = await classifier.classifyIntent('Create a budget spreadsheet');

      expect(result.type).toBe('TASK');
      expect(result.confidence).toBeGreaterThan(0.5);
      expect(result.context.taskType).toBeDefined();
    });

    test('should classify analysis intents', async () => {
      const result = await classifier.classifyIntent('Check my formulas for errors');

      expect(result.type).toBe('ANALYSIS');
      expect(result.confidence).toBeGreaterThan(0.5);
      expect(result.context.analysisTarget).toBeDefined();
    });

    test('should detect financial modeling tasks', async () => {
      const result = await classifier.classifyIntent('Build a DCF model');

      expect(result.type).toBe('TASK');
      expect(result.context.taskType).toBe('financial_modeling');
      expect(result.context.keywords).toContain('dcf');
    });

    test('should estimate task complexity', async () => {
      const simpleTask = await classifier.classifyIntent('Sum column A');
      const complexTask = await classifier.classifyIntent('Create a comprehensive financial model with DCF analysis, scenario planning, and sensitivity analysis');

      expect(simpleTask.context.complexity).toBeLessThan(complexTask.context.complexity);
    });
  });

  describe('Clarification Detection', () => {
    test('should detect when clarification is needed', async () => {
      const result = await classifier.classifyIntent('Make it better');

      expect(result.needsClarification).toBe(true);
    });

    test('should not require clarification for clear requests', async () => {
      const result = await classifier.classifyIntent('Create a sum formula in cell A1');

      expect(result.needsClarification).toBe(false);
    });
  });
});

describe('TaskPlanner', () => {
  let planner: TaskPlanner;

  beforeEach(() => {
    planner = new TaskPlanner();
  });

  describe('Execution Plan Creation', () => {
    test('should create plan for financial modeling', async () => {
      const intent = {
        type: 'TASK' as const,
        confidence: 0.9,
        needsClarification: false,
        context: {
          taskType: 'financial_modeling',
          complexity: 5,
          keywords: ['dcf', 'model', 'financial'],
          entities: [],
        },
      };

      const plan = await planner.createExecutionPlan(intent);

      expect(plan).toBeDefined();
      expect(plan.steps.length).toBeGreaterThan(0);
      expect(plan.complexity).toBeGreaterThan(0);
      expect(plan.title).toContain('Financial');
    });

    test('should create plan for data processing', async () => {
      const intent = {
        type: 'TASK' as const,
        confidence: 0.8,
        needsClarification: false,
        context: {
          taskType: 'data_processing',
          complexity: 3,
          keywords: ['clean', 'data', 'transform'],
          entities: [],
        },
      };

      const plan = await planner.createExecutionPlan(intent);

      expect(plan).toBeDefined();
      expect(plan.steps.some(step => step.type === 'data_cleaning')).toBe(true);
    });

    test('should include mandatory steps', async () => {
      const intent = {
        type: 'TASK' as const,
        confidence: 0.9,
        needsClarification: false,
        context: {
          taskType: 'financial_modeling',
          complexity: 4,
          dataSource: 'pdf',
          keywords: ['model'],
          entities: [],
        },
      };

      const plan = await planner.createExecutionPlan(intent);

      expect(plan.steps.some(step => step.type === 'attribution')).toBe(true);
      expect(plan.steps.some(step => step.type === 'validation')).toBe(true);
    });

    test('should sort steps by dependencies', async () => {
      const intent = {
        type: 'TASK' as const,
        confidence: 0.9,
        needsClarification: false,
        context: {
          taskType: 'data_processing',
          complexity: 3,
          keywords: ['import', 'clean', 'analyze'],
          entities: [],
        },
      };

      const plan = await planner.createExecutionPlan(intent);

      // Find import and clean steps
      const importStep = plan.steps.find(step => step.description.includes('Import'));
      const cleanStep = plan.steps.find(step => step.description.includes('Clean'));

      if (importStep && cleanStep) {
        const importIndex = plan.steps.indexOf(importStep);
        const cleanIndex = plan.steps.indexOf(cleanStep);
        expect(importIndex).toBeLessThan(cleanIndex);
      }
    });
  });

  describe('Plan Complexity Calculation', () => {
    test('should calculate complexity correctly', async () => {
      const simpleIntent = {
        type: 'TASK' as const,
        confidence: 0.9,
        needsClarification: false,
        context: {
          taskType: 'calculation',
          complexity: 1,
          keywords: ['sum'],
          entities: [],
        },
      };

      const complexIntent = {
        type: 'TASK' as const,
        confidence: 0.9,
        needsClarification: false,
        context: {
          taskType: 'financial_modeling',
          complexity: 8,
          keywords: ['dcf', 'model', 'scenario', 'analysis'],
          entities: [],
        },
      };

      const simplePlan = await planner.createExecutionPlan(simpleIntent);
      const complexPlan = await planner.createExecutionPlan(complexIntent);

      expect(simplePlan.complexity).toBeLessThan(complexPlan.complexity);
    });
  });
});

describe('ResponseGenerator', () => {
  let generator: ResponseGenerator;

  beforeEach(() => {
    generator = new ResponseGenerator();
  });

  describe('Chat Responses', () => {
    test('should generate greeting responses', async () => {
      const response = await generator.generateChatResponse('Hello', {
        keywords: ['hello'],
        entities: [],
      });

      expect(response).toContain('spreadsheet');
      expect(response.length).toBeGreaterThan(10);
    });

    test('should generate help responses', async () => {
      const response = await generator.generateChatResponse('What can you help me with?', {
        keywords: ['help'],
        entities: [],
      });

      expect(response).toContain('Financial Modeling');
      expect(response).toContain('Data Processing');
      expect(response).toContain('Visualizations');
    });
  });

  describe('Clarification Requests', () => {
    test('should generate clarification for financial modeling', async () => {
      const response = await generator.generateClarificationRequest('Create a model', {
        taskType: 'financial_modeling',
        keywords: ['model'],
        entities: [],
      });

      expect(response).toContain('financial model');
      expect(response).toContain('DCF');
      expect(response).toContain('time period');
    });

    test('should generate clarification for data processing', async () => {
      const response = await generator.generateClarificationRequest('Process my data', {
        taskType: 'data_processing',
        keywords: ['data'],
        entities: [],
      });

      expect(response).toContain('data processing');
      expect(response).toContain('format');
      expect(response).toContain('output');
    });
  });

  describe('Task Responses', () => {
    test('should generate task completion responses', async () => {
      const mockPlan = {
        id: 'plan-1',
        title: 'Financial Model Creation',
        description: 'Create DCF model',
        complexity: 5,
        estimatedDuration: 15,
        steps: [],
        dependencies: [],
        checkpoints: [],
      };

      const mockActions = [
        {
          type: 'spreadsheet_update' as const,
          description: 'Created model structure',
          result: { success: true },
        },
        {
          type: 'spreadsheet_update' as const,
          description: 'Added formulas',
          result: { success: true },
        },
      ];

      const response = await generator.generateTaskResponse(mockPlan, mockActions, {
        taskType: 'financial_modeling',
        keywords: ['dcf'],
        entities: [],
      });

      expect(response).toContain('completed');
      expect(response).toContain('financial modeling');
      expect(response).toContain('2 steps');
      expect(response).toContain('checkpoint');
    });
  });

  describe('Analysis Responses', () => {
    test('should generate analysis responses with findings', async () => {
      const mockResult = {
        errors: [
          { description: 'Circular reference found', location: 'A1' },
        ],
        insights: [
          { description: 'Strong correlation detected' },
        ],
        recommendations: [
          { description: 'Add data validation' },
        ],
      };

      const response = await generator.generateAnalysisResponse(mockResult, {
        analysisTarget: 'formulas',
        keywords: ['analyze'],
        entities: [],
      });

      expect(response).toContain('Issues Found');
      expect(response).toContain('Key Insights');
      expect(response).toContain('Recommendations');
      expect(response).toContain('Circular reference');
    });

    test('should generate positive analysis responses', async () => {
      const mockResult = {
        errors: [],
        insights: [],
        recommendations: [],
      };

      const response = await generator.generateAnalysisResponse(mockResult, {
        analysisTarget: 'general',
        keywords: ['check'],
        entities: [],
      });

      expect(response).toContain('Everything looks good');
    });
  });
});
