/**
 * Spreadsheet Tests - Test workbook, worksheet, and execution engine
 */

import { Workbook, WorkbookManager } from '../src/spreadsheet/workbook';
import { Worksheet } from '../src/spreadsheet/worksheet';
import { ExecutionEngine } from '../src/spreadsheet/execution_engine';

describe('Worksheet', () => {
  let worksheet: Worksheet;

  beforeEach(() => {
    worksheet = new Worksheet('Test Sheet');
  });

  describe('Basic Properties', () => {
    test('should have correct initial properties', () => {
      expect(worksheet.name).toBe('Test Sheet');
      expect(worksheet.id).toBeDefined();
      expect(worksheet.visible).toBe(true);
      expect(worksheet.protected).toBe(false);
    });

    test('should track row and column count', () => {
      worksheet.setCell('A1', 'test');
      worksheet.setCell('B2', 'test2');
      
      expect(worksheet.rowCount).toBe(2);
      expect(worksheet.columnCount).toBe(2);
    });
  });

  describe('Cell Operations', () => {
    test('should set and get cell values', () => {
      worksheet.setCell('A1', 'Hello World');
      const cell = worksheet.getCell('A1');
      
      expect(cell.value).toBe('Hello World');
      expect(cell.type).toBe('string');
    });

    test('should handle different data types', () => {
      worksheet.setCell('A1', 42);
      worksheet.setCell('B1', true);
      worksheet.setCell('C1', new Date('2024-01-01'));
      
      expect(worksheet.getCell('A1').type).toBe('number');
      expect(worksheet.getCell('B1').type).toBe('boolean');
      expect(worksheet.getCell('C1').type).toBe('date');
    });

    test('should set formulas', () => {
      worksheet.setCellFormula('A1', '=SUM(B1:B10)');
      const cell = worksheet.getCell('A1');
      
      expect(cell.formula).toBe('=SUM(B1:B10)');
      expect(cell.type).toBe('formula');
    });

    test('should apply cell styles', () => {
      const style = {
        font: { bold: true, color: 'red' },
        fill: { type: 'solid' as const, color: 'yellow' },
      };
      
      worksheet.setCellStyle('A1', style);
      const cell = worksheet.getCell('A1');
      
      expect(cell.style?.font?.bold).toBe(true);
      expect(cell.style?.font?.color).toBe('red');
    });

    test('should handle row and column notation', () => {
      worksheet.setCell(1, 'test', 1); // Row 1, Column 1
      const cell = worksheet.getCell('A1');
      
      expect(cell.value).toBe('test');
    });
  });

  describe('Range Operations', () => {
    test('should set and get ranges', () => {
      const data = [
        ['A', 'B', 'C'],
        [1, 2, 3],
        [4, 5, 6],
      ];
      
      worksheet.setRange('A1:C3', data);
      const range = worksheet.getRange('A1:C3');
      
      expect(range.length).toBe(3);
      expect(range[0].length).toBe(3);
      expect(range[0][0].value).toBe('A');
      expect(range[2][2].value).toBe(6);
    });

    test('should clear ranges', () => {
      worksheet.setCell('A1', 'test');
      worksheet.setCell('B1', 'test2');
      
      worksheet.clearRange('A1:B1');
      
      expect(worksheet.getCell('A1').value).toBeNull();
      expect(worksheet.getCell('B1').value).toBeNull();
    });
  });

  describe('Charts', () => {
    test('should add charts', () => {
      const chart = worksheet.addChart({
        type: 'column',
        title: 'Test Chart',
        data: {
          series: [{ name: 'Series 1', values: [1, 2, 3] }],
        },
        position: { x: 100, y: 100 },
        size: { width: 400, height: 300 },
      });
      
      expect(chart.id).toBeDefined();
      expect(chart.title).toBe('Test Chart');
      expect(worksheet.getCharts().length).toBe(1);
    });

    test('should remove charts', () => {
      const chart = worksheet.addChart({
        type: 'line',
        data: { series: [] },
        position: { x: 0, y: 0 },
        size: { width: 100, height: 100 },
      });
      
      const removed = worksheet.removeChart(chart.id);
      
      expect(removed).toBe(true);
      expect(worksheet.getCharts().length).toBe(0);
    });
  });

  describe('Named Ranges', () => {
    test('should add named ranges', () => {
      worksheet.addNamedRange('TestRange', {
        start: { row: 1, column: 1 },
        end: { row: 10, column: 1 },
      });
      
      const namedRange = worksheet.getNamedRange('TestRange');
      expect(namedRange).toBeDefined();
      expect(namedRange?.name).toBe('TestRange');
    });

    test('should remove named ranges', () => {
      worksheet.addNamedRange('TestRange', {
        start: { row: 1, column: 1 },
        end: { row: 10, column: 1 },
      });
      
      const removed = worksheet.removeNamedRange('TestRange');
      expect(removed).toBe(true);
      expect(worksheet.getNamedRange('TestRange')).toBeUndefined();
    });
  });

  describe('Events', () => {
    test('should emit cell change events', (done) => {
      worksheet.on('cell_change', (event) => {
        expect(event.type).toBe('cell_change');
        expect(event.newValue).toBe('test value');
        done();
      });
      
      worksheet.setCell('A1', 'test value');
    });
  });

  describe('Serialization', () => {
    test('should serialize and deserialize', () => {
      worksheet.setCell('A1', 'test');
      worksheet.setCell('B1', 42);
      
      const json = worksheet.toJSON();
      const restored = Worksheet.fromJSON(json);
      
      expect(restored.name).toBe(worksheet.name);
      expect(restored.getCell('A1').value).toBe('test');
      expect(restored.getCell('B1').value).toBe(42);
    });
  });
});

describe('Workbook', () => {
  let workbook: Workbook;

  beforeEach(() => {
    workbook = new Workbook({ title: 'Test Workbook' });
  });

  describe('Basic Properties', () => {
    test('should have correct initial properties', () => {
      expect(workbook.title).toBe('Test Workbook');
      expect(workbook.id).toBeDefined();
      expect(workbook.worksheetCount).toBe(1); // Default sheet
    });

    test('should track creation and modification dates', () => {
      expect(workbook.created).toBeInstanceOf(Date);
      expect(workbook.modified).toBeInstanceOf(Date);
    });
  });

  describe('Worksheet Management', () => {
    test('should add worksheets', () => {
      const worksheet = workbook.addWorksheet('New Sheet');
      
      expect(worksheet.name).toBe('New Sheet');
      expect(workbook.worksheetCount).toBe(2);
    });

    test('should ensure unique worksheet names', () => {
      workbook.addWorksheet('Sheet1'); // This should become Sheet11 since Sheet1 exists
      const worksheet = workbook.getWorksheetByName('Sheet11');
      
      expect(worksheet).toBeDefined();
    });

    test('should get worksheets by name and index', () => {
      const worksheet = workbook.addWorksheet('Test Sheet');
      
      expect(workbook.getWorksheetByName('Test Sheet')).toBe(worksheet);
      expect(workbook.getWorksheetByIndex(1)).toBe(worksheet);
    });

    test('should set active worksheet', () => {
      const worksheet = workbook.addWorksheet('Active Sheet');
      const success = workbook.setActiveWorksheet(worksheet.id);
      
      expect(success).toBe(true);
      expect(workbook.getActiveWorksheet()).toBe(worksheet);
    });

    test('should remove worksheets', () => {
      const worksheet = workbook.addWorksheet('To Remove');
      const removed = workbook.removeWorksheet(worksheet.id);
      
      expect(removed).toBe(true);
      expect(workbook.getWorksheet(worksheet.id)).toBeUndefined();
    });

    test('should not remove the last worksheet', () => {
      // Remove all but one worksheet
      const worksheets = workbook.getWorksheets();
      for (let i = 1; i < worksheets.length; i++) {
        workbook.removeWorksheet(worksheets[i].id);
      }
      
      expect(() => {
        workbook.removeWorksheet(worksheets[0].id);
      }).toThrow('Cannot remove the last worksheet');
    });
  });

  describe('Checkpoints', () => {
    test('should create checkpoints', () => {
      const checkpointId = workbook.createCheckpoint('Test checkpoint');
      
      expect(checkpointId).toBeDefined();
      expect(workbook.getCheckpoints().length).toBe(1);
    });

    test('should restore checkpoints', () => {
      // Make some changes
      workbook.addWorksheet('Test Sheet');
      const checkpointId = workbook.createCheckpoint();
      
      // Make more changes
      workbook.addWorksheet('Another Sheet');
      
      // Restore checkpoint
      const restored = workbook.restoreCheckpoint(checkpointId);
      
      expect(restored).toBe(true);
      expect(workbook.getWorksheetByName('Another Sheet')).toBeUndefined();
    });

    test('should remove checkpoints', () => {
      const checkpointId = workbook.createCheckpoint();
      const removed = workbook.removeCheckpoint(checkpointId);
      
      expect(removed).toBe(true);
      expect(workbook.getCheckpoints().length).toBe(0);
    });
  });

  describe('Events', () => {
    test('should emit worksheet events', (done) => {
      workbook.on('worksheet_added', (event) => {
        expect(event.type).toBe('worksheet_added');
        expect(event.worksheetName).toBe('Event Test');
        done();
      });
      
      workbook.addWorksheet('Event Test');
    });
  });

  describe('Serialization', () => {
    test('should serialize and deserialize', () => {
      workbook.addWorksheet('Test Sheet');
      const worksheet = workbook.getWorksheetByName('Test Sheet');
      worksheet?.setCell('A1', 'test data');
      
      const json = workbook.toJSON();
      const restored = Workbook.fromJSON(json);
      
      expect(restored.title).toBe(workbook.title);
      expect(restored.worksheetCount).toBe(workbook.worksheetCount);
      
      const restoredWorksheet = restored.getWorksheetByName('Test Sheet');
      expect(restoredWorksheet?.getCell('A1').value).toBe('test data');
    });
  });
});

describe('WorkbookManager', () => {
  let manager: WorkbookManager;

  beforeEach(async () => {
    manager = new WorkbookManager();
    await manager.initialize();
  });

  afterEach(async () => {
    await manager.cleanup();
  });

  describe('Workbook Management', () => {
    test('should create workbooks', () => {
      const workbook = manager.createWorkbook({ title: 'Test Workbook' });
      
      expect(workbook.title).toBe('Test Workbook');
      expect(manager.getWorkbook(workbook.id)).toBe(workbook);
    });

    test('should set active workbook', () => {
      const workbook = manager.createWorkbook();
      const success = manager.setActiveWorkbook(workbook.id);
      
      expect(success).toBe(true);
      expect(manager.getActiveWorkbook()).toBe(workbook);
    });

    test('should remove workbooks', () => {
      const workbook = manager.createWorkbook();
      const removed = manager.removeWorkbook(workbook.id);
      
      expect(removed).toBe(true);
      expect(manager.getWorkbook(workbook.id)).toBeUndefined();
    });

    test('should create checkpoints', async () => {
      const workbook = manager.createWorkbook();
      const checkpointId = await manager.createCheckpoint(workbook.id);
      
      expect(checkpointId).toBeDefined();
    });
  });
});

describe('ExecutionEngine', () => {
  let engine: ExecutionEngine;

  beforeEach(() => {
    engine = new ExecutionEngine();
  });

  afterEach(() => {
    engine.dispose();
  });

  describe('Code Execution', () => {
    test('should execute simple JavaScript', async () => {
      const result = await engine.execute('return 1 + 1;', {} as any);
      
      expect(result.value).toBe(2);
      expect(result.errors.length).toBe(0);
      expect(result.executionTime).toBeGreaterThan(0);
    });

    test('should handle execution errors', async () => {
      const result = await engine.execute('throw new Error("Test error");', {} as any);
      
      expect(result.value).toBeNull();
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors[0]).toContain('Test error');
    });

    test('should provide sandbox utilities', async () => {
      const code = `
        const col = utils.columnToNumber('A');
        const letter = utils.numberToColumn(1);
        return { col, letter };
      `;
      
      const result = await engine.execute(code, {} as any);
      
      expect(result.value.col).toBe(1);
      expect(result.value.letter).toBe('A');
    });

    test('should handle assertions', async () => {
      const code = `
        assert(1 === 1, 'This should pass');
        assert(1 === 2, 'This should fail');
        return 'done';
      `;
      
      const result = await engine.execute(code, {} as any);
      
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors[0]).toContain('This should fail');
    });
  });

  describe('Formula Execution', () => {
    test('should execute Excel formulas', async () => {
      const mockContext = {
        workbook: {},
        worksheet: {
          getRange: () => ({
            values: [[1, 2, 3], [4, 5, 6]],
          }),
        },
      };
      
      const result = await engine.executeFormula('=SUM(A1:A2)', mockContext as any);
      
      expect(result.value).toBeDefined();
    });

    test('should convert cell references', async () => {
      const mockContext = {
        workbook: {},
        worksheet: {
          getCell: (addr: string) => ({ value: addr === 'A1' ? 10 : 20 }),
        },
      };
      
      const result = await engine.executeFormula('=A1+B1', mockContext as any);
      
      expect(result.value).toBe(30);
    });
  });

  describe('Security', () => {
    test('should prevent dangerous operations', async () => {
      const dangerousCode = 'require("fs").readFileSync("/etc/passwd");';
      
      const result = await engine.execute(dangerousCode, {} as any);
      
      expect(result.errors.length).toBeGreaterThan(0);
    });

    test('should timeout long-running code', async () => {
      const longRunningCode = 'while(true) {}';
      
      const result = await engine.execute(longRunningCode, {} as any);
      
      expect(result.errors.length).toBeGreaterThan(0);
    }, 35000); // Allow extra time for timeout
  });

  describe('Memory Management', () => {
    test('should track memory usage', () => {
      const memoryUsage = engine.getMemoryUsage();
      expect(typeof memoryUsage).toBe('number');
    });
  });
});
