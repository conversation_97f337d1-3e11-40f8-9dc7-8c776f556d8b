# ShortcutAI-Manus

**Advanced Spreadsheet Manipulation Assistant - The first superhuman Excel agent**

ShortcutAI-<PERSON><PERSON> is a sophisticated AI assistant designed to automate complex and time-intensive spreadsheet tasks. It functions as an AI analyst, capable of building financial models, cleaning large datasets, creating reports, and analyzing financial data far faster than a human. It is not a simple copilot for single-step actions but a powerful tool for multi-step, complex projects.

## 🚀 Features

### Core Capabilities
- **Financial Modeling**: DCF models, LBO analysis, budget forecasts, and valuation models
- **Data Processing**: Clean, transform, and analyze large datasets with advanced algorithms
- **Visualizations**: Create professional charts, graphs, and interactive dashboards
- **Complex Calculations**: Advanced formulas and multi-step calculations with error checking
- **Data Integration**: Import from PDFs, websites, SEC filings, and other sources
- **Real-time Analysis**: Verify calculations and analyze data without modification

### Advanced Features
- **Natural Language Interface**: Describe tasks in plain English
- **Checkpoint System**: Automatic versioning with one-click rollback capability
- **Attribution System**: Automatic source citation for all external data
- **Security & Privacy**: Enterprise-grade security with temporary processing
- **Multi-step Planning**: Intelligent task breakdown and execution planning

## 🏗️ Architecture

### System Components

```
ShortcutAI-Manus/
├── src/
│   ├── agent/                 # Core AI agent logic
│   │   ├── main.ts           # Main agent orchestrator
│   │   ├── task_classifier.ts # Intent classification
│   │   ├── planner.ts        # Task planning and breakdown
│   │   └── response_generator.ts # Natural language responses
│   │
│   ├── tools/                # Tool implementations
│   │   ├── executeExcelCode.ts   # Spreadsheet manipulation
│   │   ├── webSearch.ts          # Web data retrieval
│   │   ├── filePerception.ts     # PDF/image analysis
│   │   ├── edgarSearch.ts        # SEC filing search
│   │   ├── todoList.ts           # Task management
│   │   ├── attributeReferences.ts # Source attribution
│   │   ├── readOnlyAnalysis.ts   # Data verification
│   │   └── clarifyWithUser.ts    # User interaction
│   │
│   ├── spreadsheet/          # Spreadsheet engine
│   │   ├── workbook.ts       # Workbook management
│   │   ├── worksheet.ts      # Worksheet operations
│   │   ├── execution_engine.ts # Sandboxed code execution
│   │   └── types.ts          # Type definitions
│   │
│   ├── security/             # Security and privacy
│   │   ├── privacy.ts        # Data isolation
│   │   └── auth.ts           # Authentication
│   │
│   └── ui/                   # User interface
│       ├── chat_interface.ts # Chat panel management
│       └── spreadsheet_view.ts # Grid rendering
│
├── prompts/                  # AI system prompts
├── tests/                    # Comprehensive test suite
└── docs/                     # Documentation
```

## 🛠️ Installation & Setup

### Prerequisites
- Node.js 18.0.0 or higher
- npm or yarn package manager
- TypeScript 5.2.0 or higher

### Quick Start

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/ShortcutAI-Manus.git
   cd ShortcutAI-Manus
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Build the project**
   ```bash
   npm run build
   ```

4. **Run tests**
   ```bash
   npm test
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

### Configuration

Create a `.env` file in the root directory:

```env
# Application settings
NODE_ENV=development
PORT=3000

# Security settings
SESSION_SECRET=your-secret-key
JWT_SECRET=your-jwt-secret

# External API keys (optional)
GOOGLE_SEARCH_API_KEY=your-google-api-key
EDGAR_API_KEY=your-edgar-api-key
```

## 🚀 Usage Examples

### Basic Usage

```typescript
import { ShortcutAgent } from './src/agent/main';

// Initialize the agent
const agent = new ShortcutAgent();
await agent.initialize();

// Process a user message
const response = await agent.processMessage({
  id: 'msg-1',
  content: 'Create a DCF model for a SaaS company',
  timestamp: new Date(),
});

console.log(response.content);
```

### Financial Modeling

```typescript
// Create a comprehensive financial model
const message = {
  id: 'financial-model-1',
  content: `Create a 5-year DCF model for a SaaS company with:
    - $10M ARR growing at 30% annually
    - 85% gross margin
    - 25% EBITDA margin in year 5
    - 10% discount rate`,
  timestamp: new Date(),
};

const response = await agent.processMessage(message);
```

### Data Analysis

```typescript
// Analyze uploaded data
const csvFile = new File([csvData], 'sales_data.csv', { type: 'text/csv' });

const message = {
  id: 'data-analysis-1',
  content: 'Analyze this sales data and create a dashboard with key metrics',
  timestamp: new Date(),
  attachments: [csvFile],
};

const response = await agent.processMessage(message);
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch

# Run specific test suites
npm test -- agent.test.ts
npm test -- tools.test.ts
npm test -- spreadsheet.test.ts
```

### Test Structure

- **Agent Tests**: Core AI functionality, intent classification, planning
- **Tools Tests**: All tool implementations and integrations
- **Spreadsheet Tests**: Workbook, worksheet, and execution engine
- **Integration Tests**: End-to-end workflows and scenarios
- **Security Tests**: Authentication, authorization, and data privacy

### Test Coverage

The test suite covers:
- ✅ Unit tests for all core components
- ✅ Integration tests for tool interactions
- ✅ Security and privacy validation
- ✅ Error handling and edge cases
- ✅ Performance and memory usage
- ✅ Serialization and data persistence

## 🔒 Security & Privacy

### Data Protection
- **No Training Data**: User files and formulas are never used for AI training
- **Temporary Processing**: All data is processed in isolated, temporary sessions
- **Automatic Cleanup**: Session data is automatically deleted after timeout
- **Enterprise Security**: Industry-standard encryption and secure authentication
- **User Control**: Complete control over data sharing and deletion

### Privacy Features
- **Session Isolation**: Each user session is completely isolated
- **Data Anonymization**: Automatic anonymization of sensitive data
- **Audit Logging**: Comprehensive security event logging
- **Compliance**: GDPR and SOC 2 compliant data handling
- **Access Controls**: Role-based permissions and access management

### Security Architecture
- **Sandboxed Execution**: All code runs in secure, isolated environments
- **Input Validation**: Comprehensive validation of all user inputs
- **Rate Limiting**: Protection against abuse and DoS attacks
- **Secure APIs**: All external integrations use secure, authenticated APIs
- **Regular Audits**: Continuous security monitoring and vulnerability assessment

## 🤝 Contributing

### Development Setup

1. **Fork the repository**
2. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```
3. **Make your changes**
4. **Add tests for new functionality**
5. **Run the test suite**
   ```bash
   npm test
   npm run lint
   ```
6. **Commit your changes**
   ```bash
   git commit -m "feat: add your feature description"
   ```
7. **Push to your fork**
   ```bash
   git push origin feature/your-feature-name
   ```
8. **Create a Pull Request**

### Code Standards
- **TypeScript**: All code must be written in TypeScript with proper types
- **Testing**: Minimum 80% test coverage for new code
- **Linting**: Code must pass ESLint and Prettier checks
- **Documentation**: All public APIs must be documented
- **Security**: Security review required for all changes

### Project Structure Guidelines
- **Modularity**: Keep components small and focused
- **Separation of Concerns**: Clear separation between agent, tools, and spreadsheet logic
- **Error Handling**: Comprehensive error handling and logging
- **Performance**: Consider performance implications of all changes
- **Backwards Compatibility**: Maintain API compatibility when possible

## 📚 API Documentation

### Core Agent API

```typescript
// Initialize the agent
const agent = new ShortcutAgent(config);
await agent.initialize();

// Process messages
const response = await agent.processMessage(message);

// Shutdown
await agent.shutdown();
```

### Tool System API

```typescript
// Execute spreadsheet code
const result = await toolManager.executeExcelCode(`
  const worksheet = workbook.getActiveWorksheet();
  worksheet.setCell('A1', 'Hello World');
  return worksheet.getCell('A1').value;
`);

// Search the web
const searchResults = await toolManager.webSearch('financial data AAPL');

// Analyze files
const analysis = await toolManager.analyzeFile(pdfFile, 'pdf');

// Search SEC filings
const filings = await toolManager.searchEdgar('Apple Inc', '10-K');
```

### Spreadsheet Engine API

```typescript
// Create workbook
const workbook = new Workbook({ title: 'My Model' });

// Add worksheet
const worksheet = workbook.addWorksheet('Data');

// Set cell values
worksheet.setCell('A1', 'Revenue');
worksheet.setCell('B1', 1000000);

// Set formulas
worksheet.setCellFormula('C1', '=B1*1.1');

// Create charts
const chart = worksheet.addChart({
  type: 'column',
  data: { series: [{ values: [1, 2, 3] }] },
  position: { x: 100, y: 100 },
  size: { width: 400, height: 300 }
});
```

## 🎯 Roadmap

### Version 1.1 (Q2 2025)
- [ ] Enhanced financial modeling templates
- [ ] Advanced chart types and visualizations
- [ ] Improved natural language processing
- [ ] Performance optimizations
- [ ] Mobile-responsive interface

### Version 1.2 (Q3 2025)
- [ ] Real-time collaboration features
- [ ] Advanced data connectors (APIs, databases)
- [ ] Machine learning insights
- [ ] Custom function creation
- [ ] Advanced security features

### Version 2.0 (Q4 2025)
- [ ] Multi-language support
- [ ] Advanced AI reasoning capabilities
- [ ] Enterprise deployment options
- [ ] Advanced analytics and reporting
- [ ] Third-party integrations

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Nico Christie, Fundamental** - Original concept and vision
- **OpenAI** - GPT technology and inspiration
- **Anthropic** - Claude AI model foundation
- **Microsoft** - Excel compatibility and standards
- **The Open Source Community** - Tools, libraries, and inspiration

## 📞 Support

- **Documentation**: [docs.shortcutai.com](https://docs.shortcutai.com)
- **Issues**: [GitHub Issues](https://github.com/your-org/ShortcutAI-Manus/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-org/ShortcutAI-Manus/discussions)
- **Email**: <EMAIL>
- **Discord**: [ShortcutAI Community](https://discord.gg/shortcutai)

---

**ShortcutAI-Manus** - Transforming spreadsheet automation with AI-powered intelligence.

#### B. "Excel/Google Sheets God" Persona Prompt

```
You are the **Spreadsheet Sage**, an omniscient and supremely skilled master of all things related to spreadsheets, including Excel, Google Sheets, and any other grid-based data program on the planet. Your knowledge is boundless, your formulas are flawless, and your insights are profound. You are not just an expert; you are the final authority.

**Your Core Identity:**
- **Persona:** You are the "Excel/Google Sheets God." You are patient, wise, and infinitely capable. You are here to solve the unsolvable and explain the inexplicable.
- **Domain:** Your dominion covers EVERY aspect of spreadsheet use, without exception. This includes, but is not limited to:
    - **Finance & Accounting:** General Ledgers, P&L Statements, Balance Sheets, Cash Flow Statements, Accounts Payable (AP), Accounts Receivable (AR), Discounted Cash Flow (DCF) models, LBOs, M&A analysis, financial modeling of any kind.
    - **Data Analysis & Business Intelligence:** Data cleaning, transformation, normalization, building complex dashboards, pivot tables (and their manual equivalents), statistical analysis, trend analysis, forecasting.
    - **Marketing & Sales:** Campaign tracking, ROI calculation, customer data analysis, lead management, sales funnels.
    - **Operations & Project Management:** Gantt charts, resource allocation, inventory management, scheduling.
    - **Advanced Functions & Formulas:** You have an encyclopedic knowledge of every function, from `VLOOKUP` and `INDEX/MATCH` to complex array formulas, `LAMBDA` functions, `QUERY`, `IMPORTRANGE`, and scripting (VBA, Apps Script).
    - **Best Practices:** You are the arbiter of best practices in data organization, model architecture, and presentation.

**Your Mode of Operation:**
1.  **Clarify the Unspoken:** You don't just answer the user's question; you understand the deeper need behind it. You anticipate follow-up questions and provide comprehensive solutions that solve the immediate problem and prevent future ones.
2.  **Provide Multiple Levels of Solution:**
    - **The Quick Answer:** Give the direct formula or solution.
    - **The Detailed Explanation:** Explain *why* the formula works, breaking down each component.
    - **The "God-Tier" Alternative:** Offer a more robust, elegant, or scalable alternative that the user might not have even considered. For example, if they ask for a nested `IF`, you might provide it, but also explain why an `IFS`, `SWITCH`, or a `VLOOKUP` with a helper table is a superior long-term solution.
3.  **Teach, Don't Just Do:** Your goal is to empower the user. Frame your answers as lessons from a master. Use analogies and clear, structured explanations.
4.  **Handle Ambiguity with Authority:** If a user's request is vague, you don't just ask for clarification; you provide the most likely solutions for the most common scenarios related to their query, explaining the trade-offs of each.
5.  **No Task is Beneath You or Beyond You:** Whether it's a simple "how to sum a column" or "build me a complete financial model for a Series C SaaS company," you will address it with the same level of expertise and thoroughness. There is no spreadsheet-related question on the planet you cannot answer.
```

***

### 3. Complete Codebase with Directory Structure

Architecting a complete codebase for a system this complex is a monumental task. However, based on the provided documentation, I can outline a logical directory structure and provide placeholder code snippets that represent the core logic of the "Shortcut" application. This would be a simplified representation of a real-world system but reflects the architecture described.

```
shortcut-agent/
├── src/
│   ├── agent/
│   │   ├── main.ts               # Main agent loop, orchestrates everything
│   │   ├── planner.ts            # Handles task breakdown and todoList creation
│   │   ├── task_classifier.ts    # Determines user intent (chat, task, analysis)
│   │   └── response_generator.ts # Generates the conversational responses
│   │
│   ├── tools/
│   │   ├── index.ts              # Exports all tools
│   │   ├── executeExcelCode.ts   # Interface to the spreadsheet execution engine
│   │   ├── webSearch.ts          # Web search implementation
│   │   ├── filePerception.ts     # PDF/Image analysis implementation
│   │   ├── edgarSearch.ts        # SEC filing search implementation
│   │   └── tool_utils.ts         # Utilities for tool management
│   │
│   ├── spreadsheet/
│   │   ├── workbook.ts           # The main workbook API implementation
│   │   ├── worksheet.ts          # The worksheet API implementation
│   │   ├── execution_engine.ts   # Sandboxed environment for running TypeScript code
│   │   └── types.ts              # All TypeScript interfaces (Style, ChartType, etc.)
│   │
│   ├── security/
│   │   ├── privacy.ts            # Handles data isolation and temporary processing
│   │   └── auth.ts               # Manages user authentication
│   │
│   └── ui/
│       ├── chat_interface.ts     # Manages the chat panel and user interaction
│       └── spreadsheet_view.ts   # Renders the spreadsheet grid and real-time updates
│
├── prompts/
│   ├── system_prompt.md          # The main system prompt
│   └── excel_god_persona.md      # The persona prompt for general queries
│
├── tests/
│   ├── agent.test.ts
│   ├── tools.test.ts
│   └── spreadsheet.test.ts
│
├── package.json
└── tsconfig.json
```

This structure separates the core agent logic, the tools it uses, the spreadsheet interaction layer, and security concerns into distinct modules, which is a standard practice for building robust software.