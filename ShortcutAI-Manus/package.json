{"name": "shortcut-ai-manus", "version": "1.0.0", "description": "Advanced Spreadsheet Manipulation Assistant - The first superhuman Excel agent", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["excel", "spreadsheet", "ai", "automation", "typescript", "financial-modeling"], "author": "Nico <PERSON>, Fundamental", "license": "MIT", "dependencies": {"@types/node": "^20.0.0", "axios": "^1.6.0", "cheerio": "^1.0.0-rc.12", "express": "^4.18.0", "pdf-parse": "^1.1.1", "sharp": "^0.32.0", "uuid": "^9.0.0", "ws": "^8.14.0"}, "devDependencies": {"@types/express": "^4.17.0", "@types/jest": "^29.5.0", "@types/uuid": "^9.0.0", "@types/ws": "^8.5.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.50.0", "jest": "^29.7.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.0", "typescript": "^5.2.0"}, "engines": {"node": ">=18.0.0"}}