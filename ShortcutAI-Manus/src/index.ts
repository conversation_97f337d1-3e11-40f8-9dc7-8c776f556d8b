/**
 * ShortcutAI-Manus - Advanced Spreadsheet Manipulation Assistant
 * Main entry point for the application
 */

import { ShortcutAgent } from '@/agent/main';
import { logger } from '@/utils/logger';

async function main(): Promise<void> {
  try {
    logger.info('Starting ShortcutAI-Manus...');
    
    const agent = new ShortcutAgent();
    await agent.initialize();
    
    logger.info('ShortcutAI-Manus initialized successfully');
    
    // Start the agent
    await agent.start();
    
  } catch (error) {
    logger.error('Failed to start ShortcutAI-Manus:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  logger.info('Received SIGINT, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  logger.info('Received SIGTERM, shutting down gracefully...');
  process.exit(0);
});

// Start the application
main().catch((error) => {
  logger.error('Unhandled error in main:', error);
  process.exit(1);
});
