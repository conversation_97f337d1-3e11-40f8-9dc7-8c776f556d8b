/**
 * Chat Interface - Manages the chat panel and user interaction
 */

import { EventEmitter } from 'events';
import { logger } from '@/utils/logger';

export interface ChatMessage {
  id: string;
  type: 'user' | 'agent' | 'system';
  content: string;
  timestamp: Date;
  metadata?: {
    actions?: any[];
    checkpointId?: string;
    attachments?: File[];
  };
}

export interface ChatSession {
  id: string;
  userId?: string;
  messages: ChatMessage[];
  createdAt: Date;
  lastActivity: Date;
  context?: any;
}

export interface ChatInterfaceOptions {
  maxMessages?: number;
  enableFileUpload?: boolean;
  enableVoiceInput?: boolean;
  autoSave?: boolean;
}

export class ChatInterface extends EventEmitter {
  private sessions: Map<string, ChatSession> = new Map();
  private activeSessionId?: string;
  private options: ChatInterfaceOptions;

  constructor(options: ChatInterfaceOptions = {}) {
    super();
    
    this.options = {
      maxMessages: 1000,
      enableFileUpload: true,
      enableVoiceInput: false,
      autoSave: true,
      ...options,
    };

    logger.debug('ChatInterface initialized');
  }

  // Session management
  createSession(userId?: string): string {
    const sessionId = this.generateSessionId();
    
    const session: ChatSession = {
      id: sessionId,
      userId,
      messages: [],
      createdAt: new Date(),
      lastActivity: new Date(),
    };

    this.sessions.set(sessionId, session);
    this.activeSessionId = sessionId;

    // Add welcome message
    this.addSystemMessage(sessionId, this.getWelcomeMessage());

    logger.debug(`Created chat session: ${sessionId}`);
    this.emit('session_created', session);
    
    return sessionId;
  }

  getSession(sessionId: string): ChatSession | undefined {
    return this.sessions.get(sessionId);
  }

  setActiveSession(sessionId: string): boolean {
    if (!this.sessions.has(sessionId)) {
      return false;
    }
    
    this.activeSessionId = sessionId;
    this.emit('session_changed', sessionId);
    return true;
  }

  getActiveSession(): ChatSession | undefined {
    return this.activeSessionId ? this.sessions.get(this.activeSessionId) : undefined;
  }

  // Message handling
  addUserMessage(content: string, attachments?: File[], sessionId?: string): ChatMessage {
    const targetSessionId = sessionId || this.activeSessionId;
    if (!targetSessionId) {
      throw new Error('No active session');
    }

    const message: ChatMessage = {
      id: this.generateMessageId(),
      type: 'user',
      content,
      timestamp: new Date(),
      metadata: attachments ? { attachments } : undefined,
    };

    this.addMessageToSession(targetSessionId, message);
    this.emit('user_message', message, targetSessionId);
    
    return message;
  }

  addAgentMessage(content: string, metadata?: any, sessionId?: string): ChatMessage {
    const targetSessionId = sessionId || this.activeSessionId;
    if (!targetSessionId) {
      throw new Error('No active session');
    }

    const message: ChatMessage = {
      id: this.generateMessageId(),
      type: 'agent',
      content,
      timestamp: new Date(),
      metadata,
    };

    this.addMessageToSession(targetSessionId, message);
    this.emit('agent_message', message, targetSessionId);
    
    return message;
  }

  addSystemMessage(sessionId: string, content: string): ChatMessage {
    const message: ChatMessage = {
      id: this.generateMessageId(),
      type: 'system',
      content,
      timestamp: new Date(),
    };

    this.addMessageToSession(sessionId, message);
    this.emit('system_message', message, sessionId);
    
    return message;
  }

  private addMessageToSession(sessionId: string, message: ChatMessage): void {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`Session not found: ${sessionId}`);
    }

    session.messages.push(message);
    session.lastActivity = new Date();

    // Limit message history
    if (session.messages.length > this.options.maxMessages!) {
      const removeCount = session.messages.length - this.options.maxMessages!;
      session.messages.splice(0, removeCount);
    }

    this.sessions.set(sessionId, session);

    // Auto-save if enabled
    if (this.options.autoSave) {
      this.saveSession(sessionId);
    }
  }

  // Message retrieval
  getMessages(sessionId?: string): ChatMessage[] {
    const targetSessionId = sessionId || this.activeSessionId;
    if (!targetSessionId) {
      return [];
    }

    const session = this.sessions.get(targetSessionId);
    return session ? session.messages : [];
  }

  getRecentMessages(count: number, sessionId?: string): ChatMessage[] {
    const messages = this.getMessages(sessionId);
    return messages.slice(-count);
  }

  searchMessages(query: string, sessionId?: string): ChatMessage[] {
    const messages = this.getMessages(sessionId);
    const lowerQuery = query.toLowerCase();
    
    return messages.filter(message => 
      message.content.toLowerCase().includes(lowerQuery)
    );
  }

  // File handling
  async handleFileUpload(files: File[], sessionId?: string): Promise<void> {
    if (!this.options.enableFileUpload) {
      throw new Error('File upload is disabled');
    }

    const targetSessionId = sessionId || this.activeSessionId;
    if (!targetSessionId) {
      throw new Error('No active session');
    }

    for (const file of files) {
      await this.processUploadedFile(file, targetSessionId);
    }

    this.emit('files_uploaded', files, targetSessionId);
  }

  private async processUploadedFile(file: File, sessionId: string): Promise<void> {
    // Validate file
    if (!this.isValidFile(file)) {
      throw new Error(`Invalid file: ${file.name}`);
    }

    // Add system message about file upload
    this.addSystemMessage(
      sessionId, 
      `📎 Uploaded file: ${file.name} (${this.formatFileSize(file.size)})`
    );

    logger.debug(`Processed uploaded file: ${file.name}`);
  }

  private isValidFile(file: File): boolean {
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
      'text/csv', // .csv
      'application/pdf', // .pdf
      'image/jpeg', // .jpg
      'image/png', // .png
    ];

    const maxSize = 50 * 1024 * 1024; // 50MB

    return allowedTypes.includes(file.type) && file.size <= maxSize;
  }

  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Session persistence
  private async saveSession(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return;
    }

    // In a real implementation, this would save to a database
    logger.debug(`Saved session: ${sessionId}`);
  }

  async loadSession(sessionId: string): Promise<ChatSession | null> {
    // In a real implementation, this would load from a database
    return this.sessions.get(sessionId) || null;
  }

  // Utility methods
  private generateSessionId(): string {
    return `chat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getWelcomeMessage(): string {
    return `Hello! I'm your spreadsheet automation assistant. I can help you with:

📊 **Financial Modeling** - DCF models, LBO analysis, budget forecasts
🔧 **Data Processing** - Clean, transform, and analyze datasets  
📈 **Visualizations** - Create charts, graphs, and dashboards
🧮 **Complex Calculations** - Advanced formulas and multi-step calculations
📄 **Data Integration** - Import from PDFs, websites, and other sources

What would you like to work on today?`;
  }

  // Chat history management
  clearSession(sessionId?: string): boolean {
    const targetSessionId = sessionId || this.activeSessionId;
    if (!targetSessionId) {
      return false;
    }

    const session = this.sessions.get(targetSessionId);
    if (!session) {
      return false;
    }

    session.messages = [];
    session.lastActivity = new Date();
    this.sessions.set(targetSessionId, session);

    // Add new welcome message
    this.addSystemMessage(targetSessionId, this.getWelcomeMessage());

    this.emit('session_cleared', targetSessionId);
    return true;
  }

  exportSession(sessionId?: string): any {
    const targetSessionId = sessionId || this.activeSessionId;
    if (!targetSessionId) {
      return null;
    }

    const session = this.sessions.get(targetSessionId);
    if (!session) {
      return null;
    }

    return {
      sessionId: session.id,
      userId: session.userId,
      createdAt: session.createdAt,
      messageCount: session.messages.length,
      messages: session.messages.map(msg => ({
        type: msg.type,
        content: msg.content,
        timestamp: msg.timestamp,
      })),
      exportedAt: new Date(),
    };
  }

  // Statistics
  getSessionStats(sessionId?: string): {
    messageCount: number;
    userMessages: number;
    agentMessages: number;
    systemMessages: number;
    duration: number;
  } {
    const targetSessionId = sessionId || this.activeSessionId;
    if (!targetSessionId) {
      return { messageCount: 0, userMessages: 0, agentMessages: 0, systemMessages: 0, duration: 0 };
    }

    const session = this.sessions.get(targetSessionId);
    if (!session) {
      return { messageCount: 0, userMessages: 0, agentMessages: 0, systemMessages: 0, duration: 0 };
    }

    const userMessages = session.messages.filter(m => m.type === 'user').length;
    const agentMessages = session.messages.filter(m => m.type === 'agent').length;
    const systemMessages = session.messages.filter(m => m.type === 'system').length;
    const duration = session.lastActivity.getTime() - session.createdAt.getTime();

    return {
      messageCount: session.messages.length,
      userMessages,
      agentMessages,
      systemMessages,
      duration,
    };
  }

  cleanup(): void {
    this.sessions.clear();
    this.activeSessionId = undefined;
    this.removeAllListeners();
    logger.debug('ChatInterface cleaned up');
  }
}
