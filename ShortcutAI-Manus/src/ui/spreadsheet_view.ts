/**
 * Spreadsheet View - Renders the spreadsheet grid and handles real-time updates
 */

import { EventEmitter } from 'events';
import { Workbook } from '@/spreadsheet/workbook';
import { Worksheet } from '@/spreadsheet/worksheet';
import { CellValue, CellAddress, Range } from '@/spreadsheet/types';
import { logger } from '@/utils/logger';

export interface ViewportConfig {
  startRow: number;
  endRow: number;
  startColumn: number;
  endColumn: number;
}

export interface SelectionRange {
  start: CellAddress;
  end: CellAddress;
  active: CellAddress;
}

export interface SpreadsheetViewOptions {
  showGridlines?: boolean;
  showHeaders?: boolean;
  enableSelection?: boolean;
  enableEditing?: boolean;
  freezePanes?: CellAddress;
  zoomLevel?: number;
}

export class SpreadsheetView extends EventEmitter {
  private workbook?: Workbook;
  private activeWorksheet?: Worksheet;
  private viewport: ViewportConfig;
  private selection?: SelectionRange;
  private options: SpreadsheetViewOptions;
  private renderCache: Map<string, any> = new Map();

  constructor(options: SpreadsheetViewOptions = {}) {
    super();
    
    this.options = {
      showGridlines: true,
      showHeaders: true,
      enableSelection: true,
      enableEditing: true,
      zoomLevel: 1.0,
      ...options,
    };

    this.viewport = {
      startRow: 1,
      endRow: 20,
      startColumn: 1,
      endColumn: 10,
    };

    logger.debug('SpreadsheetView initialized');
  }

  // Workbook and worksheet management
  setWorkbook(workbook: Workbook): void {
    if (this.workbook) {
      this.workbook.removeAllListeners();
    }

    this.workbook = workbook;
    this.activeWorksheet = workbook.getActiveWorksheet();

    // Listen to workbook events
    this.workbook.on('cell_change', (event) => {
      this.handleCellChange(event);
    });

    this.workbook.on('worksheet_added', (event) => {
      this.emit('worksheet_added', event);
    });

    this.workbook.on('worksheet_removed', (event) => {
      this.emit('worksheet_removed', event);
    });

    this.invalidateCache();
    this.emit('workbook_changed', workbook);
    
    logger.debug(`Set workbook: ${workbook.id}`);
  }

  setActiveWorksheet(worksheetId: string): boolean {
    if (!this.workbook) {
      return false;
    }

    const worksheet = this.workbook.getWorksheet(worksheetId);
    if (!worksheet) {
      return false;
    }

    this.activeWorksheet = worksheet;
    this.workbook.setActiveWorksheet(worksheetId);
    
    this.invalidateCache();
    this.emit('worksheet_changed', worksheet);
    
    logger.debug(`Set active worksheet: ${worksheet.name}`);
    return true;
  }

  // Viewport management
  setViewport(viewport: Partial<ViewportConfig>): void {
    this.viewport = { ...this.viewport, ...viewport };
    this.invalidateCache();
    this.emit('viewport_changed', this.viewport);
  }

  scrollTo(row: number, column: number): void {
    const rowsVisible = this.viewport.endRow - this.viewport.startRow + 1;
    const columnsVisible = this.viewport.endColumn - this.viewport.startColumn + 1;

    this.setViewport({
      startRow: Math.max(1, row),
      endRow: Math.max(1, row) + rowsVisible - 1,
      startColumn: Math.max(1, column),
      endColumn: Math.max(1, column) + columnsVisible - 1,
    });
  }

  // Selection management
  setSelection(start: CellAddress, end?: CellAddress): void {
    const endAddress = end || start;
    
    this.selection = {
      start,
      end: endAddress,
      active: start,
    };

    this.emit('selection_changed', this.selection);
    logger.debug(`Selection set: ${this.formatAddress(start)} to ${this.formatAddress(endAddress)}`);
  }

  getSelection(): SelectionRange | undefined {
    return this.selection;
  }

  clearSelection(): void {
    this.selection = undefined;
    this.emit('selection_cleared');
  }

  // Cell operations
  getCellValue(address: CellAddress): CellValue | null {
    if (!this.activeWorksheet) {
      return null;
    }

    const addressString = this.formatAddress(address);
    return this.activeWorksheet.getCell(addressString);
  }

  setCellValue(address: CellAddress, value: any): void {
    if (!this.activeWorksheet) {
      return;
    }

    const addressString = this.formatAddress(address);
    this.activeWorksheet.setCell(addressString, value);
    
    // Update will be handled by the cell_change event
  }

  setCellFormula(address: CellAddress, formula: string): void {
    if (!this.activeWorksheet) {
      return;
    }

    const addressString = this.formatAddress(address);
    this.activeWorksheet.setCellFormula(addressString, formula);
  }

  // Rendering
  renderGrid(): any {
    if (!this.activeWorksheet) {
      return this.renderEmptyGrid();
    }

    const cacheKey = this.getCacheKey();
    if (this.renderCache.has(cacheKey)) {
      return this.renderCache.get(cacheKey);
    }

    const grid = this.buildGrid();
    this.renderCache.set(cacheKey, grid);
    
    return grid;
  }

  private buildGrid(): any {
    const grid = {
      headers: this.buildHeaders(),
      rows: this.buildRows(),
      viewport: this.viewport,
      selection: this.selection,
      options: this.options,
    };

    return grid;
  }

  private buildHeaders(): any {
    const headers = {
      columns: [],
      rows: [],
    };

    // Column headers (A, B, C, ...)
    if (this.options.showHeaders) {
      for (let col = this.viewport.startColumn; col <= this.viewport.endColumn; col++) {
        headers.columns.push({
          index: col,
          label: this.numberToColumnLetter(col),
          width: 100, // Default width
        });
      }

      // Row headers (1, 2, 3, ...)
      for (let row = this.viewport.startRow; row <= this.viewport.endRow; row++) {
        headers.rows.push({
          index: row,
          label: row.toString(),
          height: 25, // Default height
        });
      }
    }

    return headers;
  }

  private buildRows(): any[] {
    const rows = [];

    for (let row = this.viewport.startRow; row <= this.viewport.endRow; row++) {
      const rowData = {
        index: row,
        cells: [],
      };

      for (let col = this.viewport.startColumn; col <= this.viewport.endColumn; col++) {
        const address: CellAddress = { row, column: col };
        const cell = this.getCellValue(address);
        
        rowData.cells.push({
          address,
          value: cell?.value,
          formula: cell?.formula,
          type: cell?.type,
          style: cell?.style,
          isSelected: this.isCellSelected(address),
          isActive: this.isCellActive(address),
        });
      }

      rows.push(rowData);
    }

    return rows;
  }

  private renderEmptyGrid(): any {
    return {
      headers: { columns: [], rows: [] },
      rows: [],
      viewport: this.viewport,
      selection: null,
      options: this.options,
      empty: true,
    };
  }

  // Event handlers
  private handleCellChange(event: any): void {
    // Invalidate cache for affected area
    this.invalidateCache();
    
    // Emit view update event
    this.emit('cell_updated', {
      address: event.address,
      oldValue: event.oldValue,
      newValue: event.newValue,
    });

    // Re-render if cell is in viewport
    if (this.isCellInViewport(event.address)) {
      this.emit('render_required');
    }
  }

  // Utility methods
  private isCellSelected(address: CellAddress): boolean {
    if (!this.selection) {
      return false;
    }

    const { start, end } = this.selection;
    return address.row >= Math.min(start.row, end.row) &&
           address.row <= Math.max(start.row, end.row) &&
           address.column >= Math.min(start.column, end.column) &&
           address.column <= Math.max(start.column, end.column);
  }

  private isCellActive(address: CellAddress): boolean {
    if (!this.selection) {
      return false;
    }

    const { active } = this.selection;
    return address.row === active.row && address.column === active.column;
  }

  private isCellInViewport(address: CellAddress): boolean {
    return address.row >= this.viewport.startRow &&
           address.row <= this.viewport.endRow &&
           address.column >= this.viewport.startColumn &&
           address.column <= this.viewport.endColumn;
  }

  private formatAddress(address: CellAddress): string {
    return `${this.numberToColumnLetter(address.column)}${address.row}`;
  }

  private numberToColumnLetter(num: number): string {
    let result = '';
    while (num > 0) {
      num--;
      result = String.fromCharCode(65 + (num % 26)) + result;
      num = Math.floor(num / 26);
    }
    return result;
  }

  private getCacheKey(): string {
    return `${this.viewport.startRow}-${this.viewport.endRow}-${this.viewport.startColumn}-${this.viewport.endColumn}`;
  }

  private invalidateCache(): void {
    this.renderCache.clear();
  }

  // Configuration
  setOptions(options: Partial<SpreadsheetViewOptions>): void {
    this.options = { ...this.options, ...options };
    this.invalidateCache();
    this.emit('options_changed', this.options);
  }

  getOptions(): SpreadsheetViewOptions {
    return { ...this.options };
  }

  // Export and import
  exportView(): any {
    return {
      viewport: this.viewport,
      selection: this.selection,
      options: this.options,
      workbookId: this.workbook?.id,
      worksheetId: this.activeWorksheet?.id,
    };
  }

  importView(viewData: any): void {
    if (viewData.viewport) {
      this.viewport = viewData.viewport;
    }
    if (viewData.selection) {
      this.selection = viewData.selection;
    }
    if (viewData.options) {
      this.options = { ...this.options, ...viewData.options };
    }
    
    this.invalidateCache();
    this.emit('view_imported', viewData);
  }

  cleanup(): void {
    if (this.workbook) {
      this.workbook.removeAllListeners();
    }
    
    this.renderCache.clear();
    this.removeAllListeners();
    
    logger.debug('SpreadsheetView cleaned up');
  }
}
