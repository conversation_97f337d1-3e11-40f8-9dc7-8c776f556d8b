/**
 * Main Agent Class - Orchestrates the entire Shortcut system
 */

import { TaskClassifier, UserIntent } from './task_classifier';
import { TaskPlanner } from './planner';
import { ResponseGenerator } from './response_generator';
import { ToolManager } from '@/tools';
import { WorkbookManager } from '@/spreadsheet/workbook';
import { SecurityManager } from '@/security/privacy';
import { logger } from '@/utils/logger';

export interface AgentConfig {
  maxTaskComplexity: number;
  enableDebugMode: boolean;
  sessionTimeout: number;
}

export interface UserMessage {
  id: string;
  content: string;
  timestamp: Date;
  attachments?: File[];
}

export interface AgentResponse {
  id: string;
  content: string;
  timestamp: Date;
  actions?: AgentAction[];
  checkpointId?: string;
}

export interface AgentAction {
  type: 'spreadsheet_update' | 'web_search' | 'file_analysis' | 'edgar_search';
  description: string;
  result?: any;
}

export class ShortcutAgent {
  private config: AgentConfig;
  private taskClassifier: TaskClassifier;
  private taskPlanner: TaskPlanner;
  private responseGenerator: ResponseGenerator;
  private toolManager: ToolManager;
  private workbookManager: WorkbookManager;
  private securityManager: SecurityManager;
  private isInitialized = false;
  private currentSessionId?: string;

  constructor(config?: Partial<AgentConfig>) {
    this.config = {
      maxTaskComplexity: 10,
      enableDebugMode: false,
      sessionTimeout: 3600000, // 1 hour
      ...config,
    };

    this.taskClassifier = new TaskClassifier();
    this.taskPlanner = new TaskPlanner();
    this.responseGenerator = new ResponseGenerator();
    this.toolManager = new ToolManager();
    this.workbookManager = new WorkbookManager();
    this.securityManager = new SecurityManager();
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) {
      logger.warn('Agent already initialized');
      return;
    }

    try {
      logger.info('Initializing ShortcutAgent...');

      // Initialize all components
      await this.toolManager.initialize();
      await this.workbookManager.initialize();
      await this.securityManager.initialize();

      this.isInitialized = true;
      logger.info('ShortcutAgent initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize ShortcutAgent:', error);
      throw error;
    }
  }

  async start(): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Agent must be initialized before starting');
    }

    logger.info('ShortcutAgent started and ready to process requests');
  }

  async processMessage(message: UserMessage): Promise<AgentResponse> {
    if (!this.isInitialized) {
      throw new Error('Agent not initialized');
    }

    try {
      logger.info(`Processing message: ${message.id}`);

      // Create new session if needed
      if (!this.currentSessionId) {
        this.currentSessionId = await this.securityManager.createSession();
      }

      // Classify the user intent
      const intent = await this.taskClassifier.classifyIntent(message.content);
      logger.debug(`Classified intent: ${intent.type}`);

      // Process based on intent
      let response: AgentResponse;

      switch (intent.type) {
        case 'CHAT':
          response = await this.handleChatMode(message, intent);
          break;
        case 'TASK':
          response = await this.handleTaskMode(message, intent);
          break;
        case 'ANALYSIS':
          response = await this.handleAnalysisMode(message, intent);
          break;
        default:
          response = await this.handleUnknownIntent(message);
      }

      logger.info(`Generated response: ${response.id}`);
      return response;

    } catch (error) {
      logger.error('Error processing message:', error);
      return this.generateErrorResponse(message, error);
    }
  }

  private async handleChatMode(message: UserMessage, intent: UserIntent): Promise<AgentResponse> {
    logger.debug('Handling chat mode');
    
    const responseContent = await this.responseGenerator.generateChatResponse(
      message.content,
      intent.context
    );

    return {
      id: this.generateResponseId(),
      content: responseContent,
      timestamp: new Date(),
    };
  }

  private async handleTaskMode(message: UserMessage, intent: UserIntent): Promise<AgentResponse> {
    logger.debug('Handling task mode');

    // Check if clarification is needed
    if (intent.needsClarification) {
      const clarificationResponse = await this.responseGenerator.generateClarificationRequest(
        message.content,
        intent.context
      );

      return {
        id: this.generateResponseId(),
        content: clarificationResponse,
        timestamp: new Date(),
      };
    }

    // Create execution plan for complex tasks
    const plan = await this.taskPlanner.createExecutionPlan(intent);
    
    if (plan.complexity > this.config.maxTaskComplexity) {
      return {
        id: this.generateResponseId(),
        content: 'This task is too complex. Please break it down into smaller parts.',
        timestamp: new Date(),
      };
    }

    // Execute the plan
    const actions: AgentAction[] = [];
    let checkpointId: string | undefined;

    for (const step of plan.steps) {
      try {
        const action = await this.executeTaskStep(step);
        actions.push(action);

        // Create checkpoint after significant changes
        if (action.type === 'spreadsheet_update') {
          checkpointId = await this.workbookManager.createCheckpoint();
        }
      } catch (error) {
        logger.error(`Error executing step: ${step.description}`, error);
        break;
      }
    }

    const responseContent = await this.responseGenerator.generateTaskResponse(
      plan,
      actions,
      intent.context
    );

    return {
      id: this.generateResponseId(),
      content: responseContent,
      timestamp: new Date(),
      actions,
      checkpointId,
    };
  }

  private async handleAnalysisMode(message: UserMessage, intent: UserIntent): Promise<AgentResponse> {
    logger.debug('Handling analysis mode');

    // Perform read-only analysis
    const analysisResult = await this.toolManager.executeReadOnlyAnalysis(
      intent.context.analysisTarget
    );

    const responseContent = await this.responseGenerator.generateAnalysisResponse(
      analysisResult,
      intent.context
    );

    return {
      id: this.generateResponseId(),
      content: responseContent,
      timestamp: new Date(),
      actions: [{
        type: 'spreadsheet_update',
        description: 'Read-only analysis performed',
        result: analysisResult,
      }],
    };
  }

  private async handleUnknownIntent(message: UserMessage): Promise<AgentResponse> {
    logger.warn(`Unknown intent for message: ${message.content}`);
    
    return {
      id: this.generateResponseId(),
      content: "I'm not sure how to help with that. Could you please clarify what you'd like me to do with your spreadsheet?",
      timestamp: new Date(),
    };
  }

  private async executeTaskStep(step: any): Promise<AgentAction> {
    // This would be implemented based on the specific step type
    // For now, return a placeholder
    return {
      type: 'spreadsheet_update',
      description: step.description,
      result: { success: true },
    };
  }

  private generateErrorResponse(message: UserMessage, error: any): AgentResponse {
    return {
      id: this.generateResponseId(),
      content: 'I encountered an error while processing your request. Please try again.',
      timestamp: new Date(),
    };
  }

  private generateResponseId(): string {
    return `response_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  async shutdown(): Promise<void> {
    logger.info('Shutting down ShortcutAgent...');
    
    if (this.currentSessionId) {
      await this.securityManager.endSession(this.currentSessionId);
    }

    await this.workbookManager.cleanup();
    await this.securityManager.cleanup();
    
    this.isInitialized = false;
    logger.info('ShortcutAgent shutdown complete');
  }
}
