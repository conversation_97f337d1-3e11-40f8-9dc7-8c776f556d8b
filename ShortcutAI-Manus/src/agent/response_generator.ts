/**
 * Response Generator - Creates natural language responses for the agent
 */

import { ExecutionPlan } from './planner';
import { IntentContext } from './task_classifier';
import { AgentAction } from './main';
import { logger } from '@/utils/logger';

export class ResponseGenerator {
  private readonly systemPersonality = {
    tone: 'professional',
    style: 'helpful',
    expertise: 'spreadsheet_expert',
  };

  async generateChatResponse(userInput: string, context: IntentContext): Promise<string> {
    logger.debug('Generating chat response for input:', userInput);

    // Determine response type based on context
    if (this.isGreeting(userInput)) {
      return this.generateGreetingResponse();
    }

    if (this.isHelpRequest(userInput)) {
      return this.generateHelpResponse(context);
    }

    if (this.isCapabilityQuestion(userInput)) {
      return this.generateCapabilityResponse(context);
    }

    // Default conversational response
    return this.generateDefaultChatResponse(userInput, context);
  }

  async generateClarificationRequest(userInput: string, context: IntentContext): Promise<string> {
    logger.debug('Generating clarification request');

    const clarificationPrompts = [];

    // Ask about specific task details
    if (context.taskType === 'financial_modeling') {
      clarificationPrompts.push(
        "What type of financial model would you like me to create? (e.g., DCF, LBO, Budget forecast)",
        "What time period should the model cover?",
        "Do you have specific data sources I should use?"
      );
    } else if (context.taskType === 'data_processing') {
      clarificationPrompts.push(
        "What specific data processing do you need? (e.g., cleaning, merging, calculations)",
        "What format is your source data in?",
        "What should the final output look like?"
      );
    } else if (context.taskType === 'visualization') {
      clarificationPrompts.push(
        "What type of charts or visualizations do you need?",
        "What data should be visualized?",
        "Should this be a single chart or a dashboard?"
      );
    } else {
      clarificationPrompts.push(
        "Could you provide more details about what you'd like me to do?",
        "What specific spreadsheet operations do you need?",
        "What should the end result look like?"
      );
    }

    const selectedPrompts = clarificationPrompts.slice(0, 2); // Limit to 2 questions
    
    return `I'd be happy to help you with that! To make sure I create exactly what you need, could you clarify:\n\n` +
           selectedPrompts.map((prompt, index) => `${index + 1}. ${prompt}`).join('\n') +
           `\n\nOnce I have these details, I can get started right away.`;
  }

  async generateTaskResponse(
    plan: ExecutionPlan, 
    actions: AgentAction[], 
    context: IntentContext
  ): Promise<string> {
    logger.debug('Generating task response for plan:', plan.id);

    let response = `I've completed your ${this.getTaskTypeDescription(context.taskType)} task. `;

    // Add plan summary
    response += `Here's what I accomplished:\n\n`;

    // Summarize completed actions
    actions.forEach((action, index) => {
      response += `${index + 1}. ${action.description}\n`;
    });

    // Add results summary
    if (actions.length > 0) {
      response += `\n✅ All ${actions.length} steps completed successfully.\n`;
    }

    // Add next steps or recommendations
    if (context.taskType === 'financial_modeling') {
      response += `\nYour financial model is ready! You can now:\n`;
      response += `• Review the calculations and assumptions\n`;
      response += `• Adjust input parameters to see different scenarios\n`;
      response += `• Export the model for presentations\n`;
    } else if (context.taskType === 'data_processing') {
      response += `\nYour data has been processed! The cleaned dataset is ready for:\n`;
      response += `• Further analysis and insights\n`;
      response += `• Creating visualizations\n`;
      response += `• Exporting to other systems\n`;
    }

    // Add checkpoint information
    response += `\n💾 I've created checkpoints so you can easily revert any changes if needed.`;

    return response;
  }

  async generateAnalysisResponse(analysisResult: any, context: IntentContext): Promise<string> {
    logger.debug('Generating analysis response');

    let response = `I've completed the analysis of your spreadsheet. Here's what I found:\n\n`;

    // Add analysis findings
    if (analysisResult.errors && analysisResult.errors.length > 0) {
      response += `⚠️ **Issues Found:**\n`;
      analysisResult.errors.forEach((error: any, index: number) => {
        response += `${index + 1}. ${error.description} (${error.location})\n`;
      });
      response += `\n`;
    }

    if (analysisResult.insights && analysisResult.insights.length > 0) {
      response += `💡 **Key Insights:**\n`;
      analysisResult.insights.forEach((insight: any, index: number) => {
        response += `${index + 1}. ${insight.description}\n`;
      });
      response += `\n`;
    }

    if (analysisResult.recommendations && analysisResult.recommendations.length > 0) {
      response += `🔧 **Recommendations:**\n`;
      analysisResult.recommendations.forEach((rec: any, index: number) => {
        response += `${index + 1}. ${rec.description}\n`;
      });
    }

    if (!analysisResult.errors?.length && !analysisResult.insights?.length) {
      response += `✅ Everything looks good! Your spreadsheet appears to be well-structured with no obvious issues.`;
    }

    return response;
  }

  private isGreeting(input: string): boolean {
    const greetings = ['hello', 'hi', 'hey', 'good morning', 'good afternoon', 'good evening'];
    return greetings.some(greeting => input.toLowerCase().includes(greeting));
  }

  private isHelpRequest(input: string): boolean {
    const helpKeywords = ['help', 'what can you do', 'how do you work', 'capabilities'];
    return helpKeywords.some(keyword => input.toLowerCase().includes(keyword));
  }

  private isCapabilityQuestion(input: string): boolean {
    const capabilityKeywords = ['can you', 'are you able', 'do you support', 'what about'];
    return capabilityKeywords.some(keyword => input.toLowerCase().includes(keyword));
  }

  private generateGreetingResponse(): string {
    const greetings = [
      "Hello! I'm your spreadsheet automation assistant. I can help you build financial models, analyze data, create visualizations, and much more.",
      "Hi there! I'm here to help you with any spreadsheet tasks. What would you like to work on today?",
      "Welcome! I'm an expert at Excel and spreadsheet automation. How can I assist you?"
    ];

    return greetings[Math.floor(Math.random() * greetings.length)];
  }

  private generateHelpResponse(context: IntentContext): string {
    return `I'm an advanced spreadsheet assistant that can help you with:\n\n` +
           `📊 **Financial Modeling:** DCF models, LBO analysis, budget forecasts, and more\n` +
           `🔧 **Data Processing:** Clean, transform, and analyze large datasets\n` +
           `📈 **Visualizations:** Create charts, graphs, and interactive dashboards\n` +
           `🧮 **Complex Calculations:** Advanced formulas and multi-step calculations\n` +
           `📄 **Data Integration:** Import from PDFs, websites, and other sources\n\n` +
           `Just describe what you need, and I'll break it down into steps and execute it for you. ` +
           `I can work with existing spreadsheets or create new ones from scratch.\n\n` +
           `What would you like to work on?`;
  }

  private generateCapabilityResponse(context: IntentContext): string {
    const capabilities = [
      "Yes, I can handle complex financial modeling including DCF, LBO, and valuation models",
      "Absolutely! I can process and clean large datasets, removing duplicates and standardizing formats",
      "I can create professional charts, graphs, and dashboards with just a description",
      "I can extract data from PDFs, websites, and integrate it into your spreadsheets",
      "I can implement complex formulas and calculations across multiple sheets"
    ];

    const randomCapability = capabilities[Math.floor(Math.random() * capabilities.length)];
    
    return `${randomCapability}. I'm designed to handle both simple tasks and complex multi-step projects. ` +
           `What specific task did you have in mind?`;
  }

  private generateDefaultChatResponse(userInput: string, context: IntentContext): string {
    return `I understand you're asking about spreadsheet work. I'm here to help with any Excel or data analysis tasks. ` +
           `Could you tell me more specifically what you'd like me to help you with? ` +
           `For example, I can create financial models, process data, build charts, or analyze existing spreadsheets.`;
  }

  private getTaskTypeDescription(taskType?: string): string {
    switch (taskType) {
      case 'financial_modeling':
        return 'financial modeling';
      case 'data_processing':
        return 'data processing';
      case 'visualization':
        return 'visualization';
      case 'calculation':
        return 'calculation';
      default:
        return 'spreadsheet';
    }
  }
}
