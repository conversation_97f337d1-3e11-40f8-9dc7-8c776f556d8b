/**
 * Web Search Tool - Searches the web for current data and information
 */

import { Tool } from './index';
import axios from 'axios';
import * as cheerio from 'cheerio';
import { logger } from '@/utils/logger';

export interface WebSearchParameters {
  query: string;
  maxResults?: number;
  searchType?: 'general' | 'news' | 'financial' | 'academic';
  dateRange?: 'day' | 'week' | 'month' | 'year' | 'all';
  domain?: string;
  language?: string;
}

export interface WebSearchResult {
  success: boolean;
  query: string;
  results: SearchResultItem[];
  totalResults?: number;
  searchTime?: number;
  errors?: string[];
}

export interface SearchResultItem {
  title: string;
  url: string;
  snippet: string;
  domain: string;
  publishDate?: string;
  relevanceScore?: number;
  extractedData?: any;
}

export class WebSearchTool implements Tool {
  name = 'webSearch';
  description = 'Search the web for current data, news, financial information, and other relevant content';

  private readonly searchEngines = {
    google: 'https://www.googleapis.com/customsearch/v1',
    bing: 'https://api.bing.microsoft.com/v7.0/search',
    duckduckgo: 'https://api.duckduckgo.com/',
  };

  validate(parameters: WebSearchParameters): boolean {
    if (!parameters || typeof parameters !== 'object') {
      return false;
    }

    if (!parameters.query || typeof parameters.query !== 'string') {
      return false;
    }

    if (parameters.query.trim().length === 0) {
      return false;
    }

    if (parameters.maxResults && (parameters.maxResults < 1 || parameters.maxResults > 50)) {
      return false;
    }

    return true;
  }

  async execute(parameters: WebSearchParameters): Promise<WebSearchResult> {
    const startTime = Date.now();

    try {
      logger.debug('Executing web search:', parameters.query);

      const searchResults = await this.performSearch(parameters);
      const enhancedResults = await this.enhanceResults(searchResults, parameters);

      const searchTime = Date.now() - startTime;

      logger.debug(`Web search completed in ${searchTime}ms, found ${enhancedResults.length} results`);

      return {
        success: true,
        query: parameters.query,
        results: enhancedResults,
        totalResults: enhancedResults.length,
        searchTime,
      };

    } catch (error) {
      const searchTime = Date.now() - startTime;
      
      logger.error('Web search failed:', error);

      return {
        success: false,
        query: parameters.query,
        results: [],
        searchTime,
        errors: [error instanceof Error ? error.message : String(error)],
      };
    }
  }

  private async performSearch(parameters: WebSearchParameters): Promise<SearchResultItem[]> {
    // For demo purposes, we'll simulate search results
    // In a real implementation, this would call actual search APIs
    
    const mockResults: SearchResultItem[] = [
      {
        title: `Search results for: ${parameters.query}`,
        url: 'https://example.com/search-result-1',
        snippet: `This is a mock search result for the query "${parameters.query}". In a real implementation, this would contain actual search results from web search APIs.`,
        domain: 'example.com',
        publishDate: new Date().toISOString(),
        relevanceScore: 0.95,
      },
      {
        title: `Related information about ${parameters.query}`,
        url: 'https://example.org/related-info',
        snippet: `Additional information related to "${parameters.query}". This would typically come from various web sources and be ranked by relevance.`,
        domain: 'example.org',
        publishDate: new Date(Date.now() - 86400000).toISOString(), // Yesterday
        relevanceScore: 0.87,
      },
    ];

    // Filter by domain if specified
    if (parameters.domain) {
      return mockResults.filter(result => result.domain.includes(parameters.domain!));
    }

    // Limit results
    const maxResults = parameters.maxResults || 10;
    return mockResults.slice(0, maxResults);
  }

  private async enhanceResults(results: SearchResultItem[], parameters: WebSearchParameters): Promise<SearchResultItem[]> {
    const enhancedResults: SearchResultItem[] = [];

    for (const result of results) {
      try {
        // Extract additional data based on search type
        let extractedData = {};

        if (parameters.searchType === 'financial') {
          extractedData = await this.extractFinancialData(result);
        } else if (parameters.searchType === 'news') {
          extractedData = await this.extractNewsData(result);
        } else {
          extractedData = await this.extractGeneralData(result);
        }

        enhancedResults.push({
          ...result,
          extractedData,
        });

      } catch (error) {
        logger.warn(`Failed to enhance result: ${result.url}`, error);
        enhancedResults.push(result);
      }
    }

    return enhancedResults;
  }

  private async extractFinancialData(result: SearchResultItem): Promise<any> {
    // Mock financial data extraction
    return {
      type: 'financial',
      metrics: {
        price: Math.random() * 1000,
        change: (Math.random() - 0.5) * 20,
        volume: Math.floor(Math.random() * 1000000),
      },
      currency: 'USD',
      lastUpdated: new Date().toISOString(),
    };
  }

  private async extractNewsData(result: SearchResultItem): Promise<any> {
    // Mock news data extraction
    return {
      type: 'news',
      category: 'business',
      sentiment: Math.random() > 0.5 ? 'positive' : 'negative',
      keyEntities: ['Company A', 'Market', 'Revenue'],
      summary: result.snippet,
    };
  }

  private async extractGeneralData(result: SearchResultItem): Promise<any> {
    // Mock general data extraction
    return {
      type: 'general',
      wordCount: result.snippet.split(' ').length,
      language: 'en',
      topics: this.extractTopics(result.snippet),
    };
  }

  private extractTopics(text: string): string[] {
    // Simple topic extraction based on common keywords
    const topics: string[] = [];
    const keywords = text.toLowerCase().split(/\s+/);

    const topicMap = {
      finance: ['money', 'revenue', 'profit', 'financial', 'investment', 'stock', 'market'],
      technology: ['software', 'tech', 'digital', 'computer', 'ai', 'data', 'algorithm'],
      business: ['company', 'business', 'corporate', 'enterprise', 'industry', 'commercial'],
      health: ['health', 'medical', 'healthcare', 'medicine', 'treatment', 'patient'],
    };

    for (const [topic, topicKeywords] of Object.entries(topicMap)) {
      if (topicKeywords.some(keyword => keywords.includes(keyword))) {
        topics.push(topic);
      }
    }

    return topics;
  }

  // Helper methods for specific search types
  async searchFinancialData(symbol: string, dataType: string = 'quote'): Promise<WebSearchResult> {
    const query = `${symbol} stock ${dataType} financial data`;
    return this.execute({
      query,
      searchType: 'financial',
      maxResults: 5,
    });
  }

  async searchCompanyInfo(companyName: string): Promise<WebSearchResult> {
    const query = `${companyName} company information revenue financials`;
    return this.execute({
      query,
      searchType: 'financial',
      maxResults: 10,
    });
  }

  async searchMarketData(market: string, timeframe: string = 'latest'): Promise<WebSearchResult> {
    const query = `${market} market data ${timeframe} trends analysis`;
    return this.execute({
      query,
      searchType: 'financial',
      maxResults: 8,
    });
  }

  async searchNews(topic: string, dateRange: string = 'week'): Promise<WebSearchResult> {
    const query = `${topic} news latest updates`;
    return this.execute({
      query,
      searchType: 'news',
      dateRange: dateRange as any,
      maxResults: 15,
    });
  }

  async searchIndustryData(industry: string, metric: string): Promise<WebSearchResult> {
    const query = `${industry} industry ${metric} statistics data trends`;
    return this.execute({
      query,
      searchType: 'general',
      maxResults: 12,
    });
  }

  private async fetchPageContent(url: string): Promise<string> {
    try {
      const response = await axios.get(url, {
        timeout: 10000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; ShortcutAI-Manus/1.0)',
        },
      });

      return response.data;
    } catch (error) {
      logger.warn(`Failed to fetch page content: ${url}`, error);
      return '';
    }
  }

  private extractTextFromHtml(html: string): string {
    try {
      const $ = cheerio.load(html);
      
      // Remove script and style elements
      $('script, style').remove();
      
      // Extract text content
      return $('body').text().trim();
    } catch (error) {
      logger.warn('Failed to extract text from HTML', error);
      return '';
    }
  }
}
