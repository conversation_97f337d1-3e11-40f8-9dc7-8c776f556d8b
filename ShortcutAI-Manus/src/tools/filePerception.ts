/**
 * File Perception Tool - Analyzes PDF and image files to extract data
 */

import { Tool } from './index';
import * as fs from 'fs';
import * as path from 'path';
import * as pdfParse from 'pdf-parse';
import { logger } from '@/utils/logger';

export interface FilePerceptionParameters {
  file: File | Buffer | string; // File object, buffer, or file path
  type?: 'pdf' | 'image' | 'auto';
  extractionMode?: 'text' | 'tables' | 'forms' | 'all';
  language?: string;
  options?: {
    preserveFormatting?: boolean;
    extractImages?: boolean;
    ocrEnabled?: boolean;
  };
}

export interface FilePerceptionResult {
  success: boolean;
  fileType: string;
  extractedText?: string;
  tables?: TableData[];
  forms?: FormData[];
  images?: ImageData[];
  metadata?: FileMetadata;
  errors?: string[];
  processingTime?: number;
}

export interface TableData {
  id: string;
  headers: string[];
  rows: string[][];
  position?: { page: number; x: number; y: number };
  confidence?: number;
}

export interface FormData {
  id: string;
  fields: FormField[];
  position?: { page: number; x: number; y: number };
}

export interface FormField {
  name: string;
  value: string;
  type: 'text' | 'number' | 'date' | 'checkbox' | 'select';
  confidence?: number;
}

export interface ImageData {
  id: string;
  description?: string;
  text?: string; // OCR extracted text
  position?: { page: number; x: number; y: number };
  size?: { width: number; height: number };
}

export interface FileMetadata {
  fileName?: string;
  fileSize?: number;
  pageCount?: number;
  createdDate?: string;
  modifiedDate?: string;
  author?: string;
  title?: string;
  subject?: string;
}

export class FilePerceptionTool implements Tool {
  name = 'filePerception';
  description = 'Analyze PDF and image files to extract text, tables, forms, and other structured data';

  validate(parameters: FilePerceptionParameters): boolean {
    if (!parameters || typeof parameters !== 'object') {
      return false;
    }

    if (!parameters.file) {
      return false;
    }

    // Validate file type if specified
    if (parameters.type && !['pdf', 'image', 'auto'].includes(parameters.type)) {
      return false;
    }

    // Validate extraction mode if specified
    if (parameters.extractionMode && !['text', 'tables', 'forms', 'all'].includes(parameters.extractionMode)) {
      return false;
    }

    return true;
  }

  async execute(parameters: FilePerceptionParameters): Promise<FilePerceptionResult> {
    const startTime = Date.now();

    try {
      logger.debug('Starting file perception analysis');

      // Determine file type
      const fileType = await this.determineFileType(parameters.file, parameters.type);
      
      // Get file buffer
      const fileBuffer = await this.getFileBuffer(parameters.file);
      
      // Extract data based on file type
      let result: FilePerceptionResult;

      switch (fileType) {
        case 'pdf':
          result = await this.processPDF(fileBuffer, parameters);
          break;
        case 'image':
          result = await this.processImage(fileBuffer, parameters);
          break;
        default:
          throw new Error(`Unsupported file type: ${fileType}`);
      }

      const processingTime = Date.now() - startTime;
      result.processingTime = processingTime;

      logger.debug(`File perception completed in ${processingTime}ms`);
      return result;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      
      logger.error('File perception failed:', error);

      return {
        success: false,
        fileType: 'unknown',
        processingTime,
        errors: [error instanceof Error ? error.message : String(error)],
      };
    }
  }

  private async determineFileType(file: any, specifiedType?: string): Promise<string> {
    if (specifiedType && specifiedType !== 'auto') {
      return specifiedType;
    }

    // Try to determine from file extension or MIME type
    if (typeof file === 'string') {
      const ext = path.extname(file).toLowerCase();
      if (ext === '.pdf') return 'pdf';
      if (['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff'].includes(ext)) return 'image';
    }

    // Check buffer magic numbers
    const buffer = await this.getFileBuffer(file);
    const header = buffer.slice(0, 8);

    // PDF magic number
    if (header.toString('ascii', 0, 4) === '%PDF') {
      return 'pdf';
    }

    // Common image magic numbers
    if (header[0] === 0xFF && header[1] === 0xD8) return 'image'; // JPEG
    if (header.toString('ascii', 0, 8) === '\x89PNG\r\n\x1a\n') return 'image'; // PNG
    if (header.toString('ascii', 0, 6) === 'GIF87a' || header.toString('ascii', 0, 6) === 'GIF89a') return 'image'; // GIF

    throw new Error('Unable to determine file type');
  }

  private async getFileBuffer(file: any): Promise<Buffer> {
    if (Buffer.isBuffer(file)) {
      return file;
    }

    if (typeof file === 'string') {
      // Assume it's a file path
      return fs.readFileSync(file);
    }

    if (file instanceof File) {
      // Browser File object
      return Buffer.from(await file.arrayBuffer());
    }

    throw new Error('Unsupported file format');
  }

  private async processPDF(buffer: Buffer, parameters: FilePerceptionParameters): Promise<FilePerceptionResult> {
    try {
      const pdfData = await pdfParse(buffer);

      const result: FilePerceptionResult = {
        success: true,
        fileType: 'pdf',
        extractedText: pdfData.text,
        metadata: {
          pageCount: pdfData.numpages,
          title: pdfData.info?.Title,
          author: pdfData.info?.Author,
          subject: pdfData.info?.Subject,
          createdDate: pdfData.info?.CreationDate,
          modifiedDate: pdfData.info?.ModDate,
        },
      };

      // Extract tables if requested
      if (parameters.extractionMode === 'tables' || parameters.extractionMode === 'all') {
        result.tables = await this.extractTablesFromText(pdfData.text);
      }

      // Extract forms if requested
      if (parameters.extractionMode === 'forms' || parameters.extractionMode === 'all') {
        result.forms = await this.extractFormsFromText(pdfData.text);
      }

      return result;

    } catch (error) {
      throw new Error(`PDF processing failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async processImage(buffer: Buffer, parameters: FilePerceptionParameters): Promise<FilePerceptionResult> {
    try {
      // For demo purposes, we'll simulate image processing
      // In a real implementation, this would use OCR libraries like Tesseract
      
      const result: FilePerceptionResult = {
        success: true,
        fileType: 'image',
        extractedText: 'This is simulated OCR text extracted from the image. In a real implementation, this would use actual OCR technology.',
        metadata: {
          fileSize: buffer.length,
        },
      };

      // Simulate table extraction from image
      if (parameters.extractionMode === 'tables' || parameters.extractionMode === 'all') {
        result.tables = [{
          id: 'table_1',
          headers: ['Column 1', 'Column 2', 'Column 3'],
          rows: [
            ['Value 1', 'Value 2', 'Value 3'],
            ['Value 4', 'Value 5', 'Value 6'],
          ],
          confidence: 0.85,
        }];
      }

      return result;

    } catch (error) {
      throw new Error(`Image processing failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async extractTablesFromText(text: string): Promise<TableData[]> {
    const tables: TableData[] = [];
    
    // Simple table detection based on patterns
    const lines = text.split('\n');
    let currentTable: string[] = [];
    let tableId = 1;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Detect table-like patterns (multiple columns separated by spaces/tabs)
      if (this.looksLikeTableRow(line)) {
        currentTable.push(line);
      } else {
        // End of table
        if (currentTable.length > 1) {
          const table = this.parseTableFromLines(currentTable, `table_${tableId++}`);
          if (table) {
            tables.push(table);
          }
        }
        currentTable = [];
      }
    }

    // Handle table at end of text
    if (currentTable.length > 1) {
      const table = this.parseTableFromLines(currentTable, `table_${tableId}`);
      if (table) {
        tables.push(table);
      }
    }

    return tables;
  }

  private looksLikeTableRow(line: string): boolean {
    // Simple heuristic: line has multiple "columns" separated by 2+ spaces or tabs
    const columns = line.split(/\s{2,}|\t/);
    return columns.length >= 2 && columns.every(col => col.trim().length > 0);
  }

  private parseTableFromLines(lines: string[], id: string): TableData | null {
    if (lines.length < 2) return null;

    const rows = lines.map(line => line.split(/\s{2,}|\t/).map(col => col.trim()));
    
    // Assume first row is headers
    const headers = rows[0];
    const dataRows = rows.slice(1);

    return {
      id,
      headers,
      rows: dataRows,
      confidence: 0.7, // Moderate confidence for text-based extraction
    };
  }

  private async extractFormsFromText(text: string): Promise<FormData[]> {
    const forms: FormData[] = [];
    
    // Simple form field detection
    const fieldPatterns = [
      /(\w+):\s*([^\n]+)/g, // "Field: Value" pattern
      /(\w+)\s*=\s*([^\n]+)/g, // "Field = Value" pattern
    ];

    let formId = 1;
    const fields: FormField[] = [];

    for (const pattern of fieldPatterns) {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        fields.push({
          name: match[1],
          value: match[2].trim(),
          type: this.guessFieldType(match[2].trim()),
          confidence: 0.6,
        });
      }
    }

    if (fields.length > 0) {
      forms.push({
        id: `form_${formId}`,
        fields,
      });
    }

    return forms;
  }

  private guessFieldType(value: string): FormField['type'] {
    // Simple type guessing based on value format
    if (/^\d+$/.test(value)) return 'number';
    if (/^\d{4}-\d{2}-\d{2}/.test(value)) return 'date';
    if (/^(yes|no|true|false|checked|unchecked)$/i.test(value)) return 'checkbox';
    return 'text';
  }

  // Helper methods for specific file types
  async extractFinancialData(file: any): Promise<FilePerceptionResult> {
    return this.execute({
      file,
      extractionMode: 'tables',
      options: { preserveFormatting: true },
    });
  }

  async extractFormData(file: any): Promise<FilePerceptionResult> {
    return this.execute({
      file,
      extractionMode: 'forms',
    });
  }

  async extractAllData(file: any): Promise<FilePerceptionResult> {
    return this.execute({
      file,
      extractionMode: 'all',
      options: { 
        preserveFormatting: true,
        extractImages: true,
        ocrEnabled: true,
      },
    });
  }
}
