/**
 * Tools Index - Exports all available tools for the agent
 */

import { ExecuteExcelCodeTool } from './executeExcelCode';
import { WebSearchTool } from './webSearch';
import { FilePerceptionTool } from './filePerception';
import { EdgarSearchTool } from './edgarSearch';
import { TodoListTool } from './todoList';
import { AttributeReferencesTool } from './attributeReferences';
import { ReadOnlyAnalysisTool } from './readOnlyAnalysis';
import { ClarifyWithUserTool } from './clarifyWithUser';
import { logger } from '@/utils/logger';

export interface Tool {
  name: string;
  description: string;
  execute(parameters: any): Promise<any>;
  validate?(parameters: any): boolean;
}

export class ToolManager {
  private tools: Map<string, Tool> = new Map();
  private isInitialized = false;

  async initialize(): Promise<void> {
    if (this.isInitialized) {
      logger.warn('ToolManager already initialized');
      return;
    }

    try {
      logger.info('Initializing ToolManager...');

      // Register all tools
      this.registerTool(new ExecuteExcelCodeTool());
      this.registerTool(new WebSearchTool());
      this.registerTool(new FilePerceptionTool());
      this.registerTool(new EdgarSearchTool());
      this.registerTool(new TodoListTool());
      this.registerTool(new AttributeReferencesTool());
      this.registerTool(new ReadOnlyAnalysisTool());
      this.registerTool(new ClarifyWithUserTool());

      this.isInitialized = true;
      logger.info(`ToolManager initialized with ${this.tools.size} tools`);
    } catch (error) {
      logger.error('Failed to initialize ToolManager:', error);
      throw error;
    }
  }

  private registerTool(tool: Tool): void {
    if (this.tools.has(tool.name)) {
      logger.warn(`Tool ${tool.name} already registered, overwriting`);
    }
    
    this.tools.set(tool.name, tool);
    logger.debug(`Registered tool: ${tool.name}`);
  }

  async executeTool(toolName: string, parameters: any): Promise<any> {
    if (!this.isInitialized) {
      throw new Error('ToolManager not initialized');
    }

    const tool = this.tools.get(toolName);
    if (!tool) {
      throw new Error(`Tool not found: ${toolName}`);
    }

    // Validate parameters if validation is available
    if (tool.validate && !tool.validate(parameters)) {
      throw new Error(`Invalid parameters for tool: ${toolName}`);
    }

    try {
      logger.debug(`Executing tool: ${toolName}`, parameters);
      const result = await tool.execute(parameters);
      logger.debug(`Tool execution completed: ${toolName}`);
      return result;
    } catch (error) {
      logger.error(`Tool execution failed: ${toolName}`, error);
      throw error;
    }
  }

  getAvailableTools(): string[] {
    return Array.from(this.tools.keys());
  }

  getToolDescription(toolName: string): string | undefined {
    const tool = this.tools.get(toolName);
    return tool?.description;
  }

  // Convenience methods for commonly used tools
  async executeExcelCode(code: string, context?: any): Promise<any> {
    return this.executeTool('executeExcelCode', { code, context });
  }

  async webSearch(query: string, options?: any): Promise<any> {
    return this.executeTool('webSearch', { query, ...options });
  }

  async analyzeFile(file: any, type?: string): Promise<any> {
    return this.executeTool('filePerception', { file, type });
  }

  async searchEdgar(query: string, options?: any): Promise<any> {
    return this.executeTool('edgarSearch', { query, ...options });
  }

  async updateTodoList(action: string, data?: any): Promise<any> {
    return this.executeTool('todoList', { action, data });
  }

  async attributeReferences(cells: any[], source: string): Promise<any> {
    return this.executeTool('attributeReferences', { cells, source });
  }

  async executeReadOnlyAnalysis(target: string, options?: any): Promise<any> {
    return this.executeTool('readOnlyAnalysis', { target, ...options });
  }

  async clarifyWithUser(question: string, options?: string[]): Promise<any> {
    return this.executeTool('clarifyWithUser', { question, options });
  }
}

// Export individual tools for direct use if needed
export {
  ExecuteExcelCodeTool,
  WebSearchTool,
  FilePerceptionTool,
  EdgarSearchTool,
  TodoListTool,
  AttributeReferencesTool,
  ReadOnlyAnalysisTool,
  ClarifyWithUserTool,
};
