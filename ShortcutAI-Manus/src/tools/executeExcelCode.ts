/**
 * Execute Excel Code Tool - Primary tool for spreadsheet manipulation
 */

import { Tool } from './index';
import { WorkbookManager } from '@/spreadsheet/workbook';
import { ExecutionEngine } from '@/spreadsheet/execution_engine';
import { logger } from '@/utils/logger';

export interface ExcelCodeParameters {
  code: string;
  context?: {
    workbookId?: string;
    worksheetName?: string;
    targetRange?: string;
    preserveFormulas?: boolean;
  };
}

export interface ExcelCodeResult {
  success: boolean;
  result?: any;
  changes?: CellChange[];
  errors?: string[];
  warnings?: string[];
  executionTime?: number;
}

export interface CellChange {
  address: string;
  oldValue: any;
  newValue: any;
  type: 'value' | 'formula' | 'format' | 'style';
}

export class ExecuteExcelCodeTool implements Tool {
  name = 'executeExcelCode';
  description = 'Execute TypeScript code to manipulate spreadsheets with full Excel API access';

  private workbookManager: WorkbookManager;
  private executionEngine: ExecutionEngine;

  constructor() {
    this.workbookManager = new WorkbookManager();
    this.executionEngine = new ExecutionEngine();
  }

  validate(parameters: ExcelCodeParameters): boolean {
    if (!parameters || typeof parameters !== 'object') {
      return false;
    }

    if (!parameters.code || typeof parameters.code !== 'string') {
      return false;
    }

    // Basic code validation
    if (parameters.code.trim().length === 0) {
      return false;
    }

    return true;
  }

  async execute(parameters: ExcelCodeParameters): Promise<ExcelCodeResult> {
    const startTime = Date.now();
    
    try {
      logger.debug('Executing Excel code:', parameters.code.substring(0, 100) + '...');

      // Get or create workbook
      const workbook = await this.getWorkbook(parameters.context?.workbookId);
      
      // Prepare execution context
      const executionContext = {
        workbook,
        worksheet: parameters.context?.worksheetName 
          ? workbook.getWorksheet(parameters.context.worksheetName)
          : workbook.getActiveWorksheet(),
        targetRange: parameters.context?.targetRange,
        preserveFormulas: parameters.context?.preserveFormulas ?? true,
      };

      // Execute the code
      const result = await this.executionEngine.execute(
        parameters.code,
        executionContext
      );

      // Track changes
      const changes = await this.trackChanges(workbook);

      const executionTime = Date.now() - startTime;

      logger.debug(`Excel code execution completed in ${executionTime}ms`);

      return {
        success: true,
        result: result.value,
        changes,
        errors: result.errors,
        warnings: result.warnings,
        executionTime,
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      logger.error('Excel code execution failed:', error);

      return {
        success: false,
        errors: [error instanceof Error ? error.message : String(error)],
        executionTime,
      };
    }
  }

  private async getWorkbook(workbookId?: string): Promise<any> {
    if (workbookId) {
      return this.workbookManager.getWorkbook(workbookId);
    }
    
    // Get active workbook or create new one
    const activeWorkbook = this.workbookManager.getActiveWorkbook();
    if (activeWorkbook) {
      return activeWorkbook;
    }

    return this.workbookManager.createWorkbook();
  }

  private async trackChanges(workbook: any): Promise<CellChange[]> {
    // This would track what cells were modified during execution
    // For now, return empty array as placeholder
    return [];
  }

  // Helper methods for common Excel operations
  async createWorksheet(name: string, workbookId?: string): Promise<ExcelCodeResult> {
    const code = `
      const worksheet = workbook.addWorksheet('${name}');
      return { worksheetId: worksheet.id, name: worksheet.name };
    `;

    return this.execute({ code, context: { workbookId } });
  }

  async setCellValue(address: string, value: any, workbookId?: string): Promise<ExcelCodeResult> {
    const code = `
      const cell = worksheet.getCell('${address}');
      cell.value = ${JSON.stringify(value)};
      return { address: '${address}', value: cell.value };
    `;

    return this.execute({ code, context: { workbookId } });
  }

  async setCellFormula(address: string, formula: string, workbookId?: string): Promise<ExcelCodeResult> {
    const code = `
      const cell = worksheet.getCell('${address}');
      cell.formula = '${formula}';
      return { address: '${address}', formula: cell.formula, value: cell.value };
    `;

    return this.execute({ code, context: { workbookId } });
  }

  async formatRange(range: string, format: any, workbookId?: string): Promise<ExcelCodeResult> {
    const code = `
      const rangeObj = worksheet.getRange('${range}');
      rangeObj.style = ${JSON.stringify(format)};
      return { range: '${range}', applied: true };
    `;

    return this.execute({ code, context: { workbookId } });
  }

  async createChart(data: any, chartType: string, workbookId?: string): Promise<ExcelCodeResult> {
    const code = `
      const chart = worksheet.addChart({
        type: '${chartType}',
        data: ${JSON.stringify(data)},
        position: { x: 100, y: 100, width: 400, height: 300 }
      });
      return { chartId: chart.id, type: '${chartType}' };
    `;

    return this.execute({ code, context: { workbookId } });
  }

  async importData(data: any[], startCell: string = 'A1', workbookId?: string): Promise<ExcelCodeResult> {
    const code = `
      const startCellObj = worksheet.getCell('${startCell}');
      const startRow = startCellObj.row;
      const startCol = startCellObj.col;
      
      const importData = ${JSON.stringify(data)};
      
      importData.forEach((row, rowIndex) => {
        if (Array.isArray(row)) {
          row.forEach((value, colIndex) => {
            const cell = worksheet.getCell(startRow + rowIndex, startCol + colIndex);
            cell.value = value;
          });
        } else {
          // Handle object rows
          Object.values(row).forEach((value, colIndex) => {
            const cell = worksheet.getCell(startRow + rowIndex, startCol + colIndex);
            cell.value = value;
          });
        }
      });
      
      return { 
        rowsImported: importData.length, 
        startCell: '${startCell}',
        endCell: worksheet.getCell(startRow + importData.length - 1, startCol + (Array.isArray(importData[0]) ? importData[0].length - 1 : Object.keys(importData[0]).length - 1)).address
      };
    `;

    return this.execute({ code, context: { workbookId } });
  }

  async calculateRange(range: string, operation: string, workbookId?: string): Promise<ExcelCodeResult> {
    const code = `
      const rangeObj = worksheet.getRange('${range}');
      const values = rangeObj.values;
      
      let result;
      switch ('${operation}') {
        case 'sum':
          result = values.flat().reduce((sum, val) => sum + (typeof val === 'number' ? val : 0), 0);
          break;
        case 'average':
          const numbers = values.flat().filter(val => typeof val === 'number');
          result = numbers.length > 0 ? numbers.reduce((sum, val) => sum + val, 0) / numbers.length : 0;
          break;
        case 'count':
          result = values.flat().filter(val => val !== null && val !== undefined && val !== '').length;
          break;
        case 'max':
          result = Math.max(...values.flat().filter(val => typeof val === 'number'));
          break;
        case 'min':
          result = Math.min(...values.flat().filter(val => typeof val === 'number'));
          break;
        default:
          throw new Error('Unsupported operation: ${operation}');
      }
      
      return { range: '${range}', operation: '${operation}', result };
    `;

    return this.execute({ code, context: { workbookId } });
  }
}
