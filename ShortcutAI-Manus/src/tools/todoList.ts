/**
 * Todo List Tool - Manages task planning and tracking
 */

import { Tool } from './index';
import { logger } from '@/utils/logger';

export interface TodoListParameters {
  action: 'create' | 'update' | 'complete' | 'get' | 'delete';
  data?: any;
}

export interface TodoItem {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high';
  createdAt: string;
  updatedAt: string;
  dueDate?: string;
  dependencies?: string[];
}

export class TodoListTool implements Tool {
  name = 'todoList';
  description = 'Manage task planning and tracking for complex multi-step operations';

  private todos: Map<string, TodoItem> = new Map();

  validate(parameters: TodoListParameters): boolean {
    return !!(parameters && parameters.action);
  }

  async execute(parameters: TodoListParameters): Promise<any> {
    switch (parameters.action) {
      case 'create':
        return this.createTodo(parameters.data);
      case 'update':
        return this.updateTodo(parameters.data);
      case 'complete':
        return this.completeTodo(parameters.data);
      case 'get':
        return this.getTodos();
      case 'delete':
        return this.deleteTodo(parameters.data);
      default:
        throw new Error(`Unknown action: ${parameters.action}`);
    }
  }

  private createTodo(data: Partial<TodoItem>): TodoItem {
    const todo: TodoItem = {
      id: this.generateId(),
      title: data.title || 'Untitled Task',
      description: data.description || '',
      status: 'pending',
      priority: data.priority || 'medium',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      dueDate: data.dueDate,
      dependencies: data.dependencies || [],
    };

    this.todos.set(todo.id, todo);
    logger.debug(`Created todo: ${todo.title}`);
    return todo;
  }

  private updateTodo(data: { id: string; updates: Partial<TodoItem> }): TodoItem {
    const todo = this.todos.get(data.id);
    if (!todo) {
      throw new Error(`Todo not found: ${data.id}`);
    }

    const updatedTodo = {
      ...todo,
      ...data.updates,
      updatedAt: new Date().toISOString(),
    };

    this.todos.set(data.id, updatedTodo);
    logger.debug(`Updated todo: ${updatedTodo.title}`);
    return updatedTodo;
  }

  private completeTodo(id: string): TodoItem {
    return this.updateTodo({
      id,
      updates: { status: 'completed' },
    });
  }

  private getTodos(): TodoItem[] {
    return Array.from(this.todos.values());
  }

  private deleteTodo(id: string): boolean {
    const deleted = this.todos.delete(id);
    if (deleted) {
      logger.debug(`Deleted todo: ${id}`);
    }
    return deleted;
  }

  private generateId(): string {
    return `todo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
