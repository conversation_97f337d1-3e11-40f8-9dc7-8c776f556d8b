/**
 * Edgar Search Tool - Searches SEC filings and financial documents
 */

import { Tool } from './index';
import axios from 'axios';
import { logger } from '@/utils/logger';

export interface EdgarSearchParameters {
  query: string;
  filingType?: string; // 10-K, 10-Q, 8-K, etc.
  company?: string;
  cik?: string; // Central Index Key
  dateRange?: {
    start: string;
    end: string;
  };
  maxResults?: number;
}

export interface EdgarSearchResult {
  success: boolean;
  query: string;
  filings: SECFiling[];
  totalResults?: number;
  searchTime?: number;
  errors?: string[];
}

export interface SECFiling {
  id: string;
  company: string;
  cik: string;
  filingType: string;
  filingDate: string;
  reportDate?: string;
  url: string;
  description: string;
  extractedData?: any;
}

export class EdgarSearchTool implements Tool {
  name = 'edgarSearch';
  description = 'Search SEC EDGAR database for company filings and financial documents';

  private readonly edgarBaseUrl = 'https://www.sec.gov/Archives/edgar';

  validate(parameters: EdgarSearchParameters): boolean {
    if (!parameters || typeof parameters !== 'object') {
      return false;
    }

    if (!parameters.query || typeof parameters.query !== 'string') {
      return false;
    }

    if (parameters.maxResults && (parameters.maxResults < 1 || parameters.maxResults > 100)) {
      return false;
    }

    return true;
  }

  async execute(parameters: EdgarSearchParameters): Promise<EdgarSearchResult> {
    const startTime = Date.now();

    try {
      logger.debug('Executing EDGAR search:', parameters.query);

      // For demo purposes, we'll simulate EDGAR search results
      // In a real implementation, this would call the actual SEC EDGAR API
      const filings = await this.searchFilings(parameters);

      const searchTime = Date.now() - startTime;

      logger.debug(`EDGAR search completed in ${searchTime}ms, found ${filings.length} filings`);

      return {
        success: true,
        query: parameters.query,
        filings,
        totalResults: filings.length,
        searchTime,
      };

    } catch (error) {
      const searchTime = Date.now() - startTime;
      
      logger.error('EDGAR search failed:', error);

      return {
        success: false,
        query: parameters.query,
        filings: [],
        searchTime,
        errors: [error instanceof Error ? error.message : String(error)],
      };
    }
  }

  private async searchFilings(parameters: EdgarSearchParameters): Promise<SECFiling[]> {
    // Mock EDGAR search results
    const mockFilings: SECFiling[] = [
      {
        id: 'filing_1',
        company: 'Example Corp',
        cik: '0001234567',
        filingType: '10-K',
        filingDate: '2024-03-15',
        reportDate: '2023-12-31',
        url: `${this.edgarBaseUrl}/data/1234567/000123456724000001/example-10k.htm`,
        description: 'Annual Report (Form 10-K) for fiscal year ended December 31, 2023',
        extractedData: {
          revenue: *********0,
          netIncome: *********,
          totalAssets: 5000000000,
          totalLiabilities: 3000000000,
          shareholdersEquity: 2000000000,
        },
      },
      {
        id: 'filing_2',
        company: 'Example Corp',
        cik: '0001234567',
        filingType: '10-Q',
        filingDate: '2024-05-10',
        reportDate: '2024-03-31',
        url: `${this.edgarBaseUrl}/data/1234567/000123456724000002/example-10q.htm`,
        description: 'Quarterly Report (Form 10-Q) for quarter ended March 31, 2024',
        extractedData: {
          revenue: *********,
          netIncome: 27500000,
          totalAssets: 5200000000,
          totalLiabilities: 3*********,
          shareholdersEquity: 2*********,
        },
      },
    ];

    // Filter by filing type if specified
    let filteredFilings = mockFilings;
    if (parameters.filingType) {
      filteredFilings = filteredFilings.filter(filing => 
        filing.filingType.toLowerCase() === parameters.filingType!.toLowerCase()
      );
    }

    // Filter by company if specified
    if (parameters.company) {
      filteredFilings = filteredFilings.filter(filing =>
        filing.company.toLowerCase().includes(parameters.company!.toLowerCase())
      );
    }

    // Filter by CIK if specified
    if (parameters.cik) {
      filteredFilings = filteredFilings.filter(filing =>
        filing.cik === parameters.cik
      );
    }

    // Filter by date range if specified
    if (parameters.dateRange) {
      const startDate = new Date(parameters.dateRange.start);
      const endDate = new Date(parameters.dateRange.end);
      
      filteredFilings = filteredFilings.filter(filing => {
        const filingDate = new Date(filing.filingDate);
        return filingDate >= startDate && filingDate <= endDate;
      });
    }

    // Limit results
    const maxResults = parameters.maxResults || 20;
    return filteredFilings.slice(0, maxResults);
  }

  // Helper methods for specific search types
  async searchCompanyFilings(company: string, filingType?: string): Promise<EdgarSearchResult> {
    return this.execute({
      query: company,
      company,
      filingType,
      maxResults: 50,
    });
  }

  async searchRecentFilings(filingType: string, days: number = 30): Promise<EdgarSearchResult> {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    return this.execute({
      query: `Recent ${filingType} filings`,
      filingType,
      dateRange: {
        start: startDate.toISOString().split('T')[0],
        end: endDate.toISOString().split('T')[0],
      },
      maxResults: 100,
    });
  }

  async searchByCIK(cik: string, filingType?: string): Promise<EdgarSearchResult> {
    return this.execute({
      query: `CIK ${cik}`,
      cik,
      filingType,
      maxResults: 50,
    });
  }

  async extractFinancialData(filing: SECFiling): Promise<any> {
    // In a real implementation, this would parse the actual filing document
    // For now, return the mock extracted data
    return filing.extractedData || {};
  }
}
