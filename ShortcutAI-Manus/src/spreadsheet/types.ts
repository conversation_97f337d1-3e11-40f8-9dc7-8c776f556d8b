/**
 * Type definitions for the spreadsheet system
 */

// Core spreadsheet types
export interface CellValue {
  value: any;
  formula?: string;
  type: 'string' | 'number' | 'boolean' | 'date' | 'error' | 'formula';
  style?: CellStyle;
  comment?: string;
  validation?: DataValidation;
}

export interface CellStyle {
  font?: FontStyle;
  fill?: FillStyle;
  border?: BorderStyle;
  alignment?: AlignmentStyle;
  numberFormat?: string;
}

export interface FontStyle {
  name?: string;
  size?: number;
  bold?: boolean;
  italic?: boolean;
  underline?: boolean;
  color?: string;
}

export interface FillStyle {
  type: 'solid' | 'gradient' | 'pattern';
  color?: string;
  backgroundColor?: string;
  pattern?: string;
}

export interface BorderStyle {
  top?: BorderLine;
  bottom?: BorderLine;
  left?: BorderLine;
  right?: BorderLine;
}

export interface BorderLine {
  style: 'thin' | 'medium' | 'thick' | 'double' | 'dotted' | 'dashed';
  color?: string;
}

export interface AlignmentStyle {
  horizontal?: 'left' | 'center' | 'right' | 'justify';
  vertical?: 'top' | 'middle' | 'bottom';
  wrapText?: boolean;
  textRotation?: number;
}

export interface DataValidation {
  type: 'list' | 'number' | 'date' | 'text' | 'custom';
  criteria: any;
  errorMessage?: string;
  showErrorMessage?: boolean;
}

// Range and address types
export interface CellAddress {
  row: number;
  column: number;
  sheet?: string;
}

export interface Range {
  start: CellAddress;
  end: CellAddress;
}

export interface NamedRange {
  name: string;
  range: Range;
  scope: 'workbook' | 'worksheet';
}

// Chart types
export interface Chart {
  id: string;
  type: ChartType;
  title?: string;
  data: ChartData;
  position: ChartPosition;
  size: ChartSize;
  options?: ChartOptions;
}

export type ChartType = 
  | 'column' | 'bar' | 'line' | 'area' | 'pie' | 'doughnut' 
  | 'scatter' | 'bubble' | 'radar' | 'combo';

export interface ChartData {
  categories?: string[] | Range;
  series: ChartSeries[];
}

export interface ChartSeries {
  name?: string;
  values: number[] | Range;
  type?: ChartType;
  color?: string;
}

export interface ChartPosition {
  x: number;
  y: number;
}

export interface ChartSize {
  width: number;
  height: number;
}

export interface ChartOptions {
  legend?: {
    position: 'top' | 'bottom' | 'left' | 'right' | 'none';
  };
  axes?: {
    x?: AxisOptions;
    y?: AxisOptions;
  };
  gridlines?: boolean;
  dataLabels?: boolean;
}

export interface AxisOptions {
  title?: string;
  min?: number;
  max?: number;
  format?: string;
}

// Worksheet types
export interface WorksheetOptions {
  name?: string;
  tabColor?: string;
  hidden?: boolean;
  protected?: boolean;
  gridlines?: boolean;
  headers?: boolean;
}

export interface WorksheetProperties {
  id: string;
  name: string;
  index: number;
  visible: boolean;
  protected: boolean;
  tabColor?: string;
  rowCount: number;
  columnCount: number;
}

// Workbook types
export interface WorkbookOptions {
  title?: string;
  author?: string;
  subject?: string;
  keywords?: string[];
  category?: string;
  comments?: string;
}

export interface WorkbookProperties {
  id: string;
  title?: string;
  author?: string;
  created: Date;
  modified: Date;
  worksheetCount: number;
  namedRanges: NamedRange[];
}

// Formula and calculation types
export interface FormulaResult {
  value: any;
  error?: FormulaError;
  dependencies?: CellAddress[];
}

export interface FormulaError {
  type: 'REF' | 'VALUE' | 'DIV0' | 'NAME' | 'NUM' | 'NA' | 'CIRCULAR';
  message: string;
  cell?: CellAddress;
}

export interface CalculationChain {
  cells: CellAddress[];
  dependencies: Map<string, string[]>;
  dirty: Set<string>;
}

// Import/Export types
export interface ImportOptions {
  format: 'xlsx' | 'csv' | 'json' | 'xml';
  hasHeaders?: boolean;
  delimiter?: string;
  encoding?: string;
  dateFormat?: string;
}

export interface ExportOptions {
  format: 'xlsx' | 'csv' | 'json' | 'pdf' | 'html';
  includeFormulas?: boolean;
  includeStyles?: boolean;
  range?: Range;
  worksheets?: string[];
}

// Event types
export interface CellChangeEvent {
  type: 'cell_change';
  address: CellAddress;
  oldValue: any;
  newValue: any;
  timestamp: Date;
}

export interface WorksheetEvent {
  type: 'worksheet_added' | 'worksheet_removed' | 'worksheet_renamed';
  worksheetId: string;
  worksheetName: string;
  timestamp: Date;
}

export interface WorkbookEvent {
  type: 'workbook_created' | 'workbook_saved' | 'workbook_closed';
  workbookId: string;
  timestamp: Date;
}

export type SpreadsheetEvent = CellChangeEvent | WorksheetEvent | WorkbookEvent;

// Execution context types
export interface ExecutionContext {
  workbook: any; // Will be the actual Workbook instance
  worksheet: any; // Will be the actual Worksheet instance
  targetRange?: string;
  preserveFormulas?: boolean;
  variables?: Map<string, any>;
}

export interface ExecutionResult {
  value: any;
  errors: string[];
  warnings: string[];
  executionTime: number;
  memoryUsage?: number;
}

// Security and permissions
export interface Permission {
  type: 'read' | 'write' | 'execute' | 'admin';
  scope: 'cell' | 'range' | 'worksheet' | 'workbook';
  target?: string;
}

export interface SecurityContext {
  userId?: string;
  sessionId: string;
  permissions: Permission[];
  restrictions?: string[];
}

// Utility types
export type CellReference = string; // e.g., "A1", "Sheet1!B2"
export type RangeReference = string; // e.g., "A1:B10", "Sheet1!A1:B10"

export interface Checkpoint {
  id: string;
  timestamp: Date;
  description: string;
  data: any; // Serialized workbook state
}

export interface UndoRedoAction {
  type: 'cell_change' | 'range_change' | 'worksheet_change' | 'workbook_change';
  description: string;
  undo: () => void;
  redo: () => void;
  timestamp: Date;
}
