/**
 * Worksheet implementation for the spreadsheet system
 */

import { 
  CellValue, CellAddress, Range, Chart, WorksheetOptions, WorksheetProperties,
  CellStyle, NamedRange, SpreadsheetEvent, CellChangeEvent
} from './types';
import { logger } from '@/utils/logger';
import { EventEmitter } from 'events';

export class Worksheet extends EventEmitter {
  private properties: WorksheetProperties;
  private cells: Map<string, CellValue> = new Map();
  private charts: Map<string, Chart> = new Map();
  private namedRanges: Map<string, NamedRange> = new Map();
  private maxRow = 0;
  private maxColumn = 0;

  constructor(name: string, options?: WorksheetOptions) {
    super();
    
    this.properties = {
      id: this.generateId(),
      name: options?.name || name,
      index: 0,
      visible: !options?.hidden,
      protected: options?.protected || false,
      tabColor: options?.tabColor,
      rowCount: 1048576, // Excel's max rows
      columnCount: 16384, // Excel's max columns
    };

    logger.debug(`Created worksheet: ${this.properties.name}`);
  }

  // Properties
  get id(): string { return this.properties.id; }
  get name(): string { return this.properties.name; }
  get index(): number { return this.properties.index; }
  get visible(): boolean { return this.properties.visible; }
  get protected(): boolean { return this.properties.protected; }
  get rowCount(): number { return this.maxRow; }
  get columnCount(): number { return this.maxColumn; }

  // Cell operations
  getCell(address: string | number, column?: number): CellValue {
    const cellAddress = this.parseCellAddress(address, column);
    const key = this.getCellKey(cellAddress);
    
    return this.cells.get(key) || {
      value: null,
      type: 'string',
    };
  }

  setCell(address: string | number, value: any, column?: number): void {
    const cellAddress = this.parseCellAddress(address, column);
    const key = this.getCellKey(cellAddress);
    
    const oldValue = this.cells.get(key);
    const newValue: CellValue = {
      value,
      type: this.inferType(value),
    };

    this.cells.set(key, newValue);
    this.updateMaxDimensions(cellAddress);

    // Emit change event
    const event: CellChangeEvent = {
      type: 'cell_change',
      address: cellAddress,
      oldValue: oldValue?.value,
      newValue: value,
      timestamp: new Date(),
    };
    this.emit('cell_change', event);

    logger.debug(`Set cell ${key} to ${value}`);
  }

  setCellFormula(address: string | number, formula: string, column?: number): void {
    const cellAddress = this.parseCellAddress(address, column);
    const key = this.getCellKey(cellAddress);
    
    const oldValue = this.cells.get(key);
    const newValue: CellValue = {
      value: null, // Will be calculated
      formula,
      type: 'formula',
    };

    this.cells.set(key, newValue);
    this.updateMaxDimensions(cellAddress);

    // Emit change event
    const event: CellChangeEvent = {
      type: 'cell_change',
      address: cellAddress,
      oldValue: oldValue?.value,
      newValue: formula,
      timestamp: new Date(),
    };
    this.emit('cell_change', event);

    logger.debug(`Set cell ${key} formula to ${formula}`);
  }

  setCellStyle(address: string | number, style: CellStyle, column?: number): void {
    const cellAddress = this.parseCellAddress(address, column);
    const key = this.getCellKey(cellAddress);
    
    const existingCell = this.cells.get(key) || { value: null, type: 'string' };
    existingCell.style = { ...existingCell.style, ...style };
    
    this.cells.set(key, existingCell);
    this.updateMaxDimensions(cellAddress);

    logger.debug(`Applied style to cell ${key}`);
  }

  // Range operations
  getRange(range: string): CellValue[][] {
    const parsedRange = this.parseRange(range);
    const result: CellValue[][] = [];

    for (let row = parsedRange.start.row; row <= parsedRange.end.row; row++) {
      const rowData: CellValue[] = [];
      for (let col = parsedRange.start.column; col <= parsedRange.end.column; col++) {
        const cellAddress: CellAddress = { row, column: col };
        const key = this.getCellKey(cellAddress);
        rowData.push(this.cells.get(key) || { value: null, type: 'string' });
      }
      result.push(rowData);
    }

    return result;
  }

  setRange(range: string, values: any[][]): void {
    const parsedRange = this.parseRange(range);
    
    for (let rowIndex = 0; rowIndex < values.length; rowIndex++) {
      const row = parsedRange.start.row + rowIndex;
      if (row > parsedRange.end.row) break;

      for (let colIndex = 0; colIndex < values[rowIndex].length; colIndex++) {
        const col = parsedRange.start.column + colIndex;
        if (col > parsedRange.end.column) break;

        this.setCell(row, values[rowIndex][colIndex], col);
      }
    }

    logger.debug(`Set range ${range} with ${values.length} rows`);
  }

  clearRange(range: string): void {
    const parsedRange = this.parseRange(range);
    
    for (let row = parsedRange.start.row; row <= parsedRange.end.row; row++) {
      for (let col = parsedRange.start.column; col <= parsedRange.end.column; col++) {
        const cellAddress: CellAddress = { row, column: col };
        const key = this.getCellKey(cellAddress);
        this.cells.delete(key);
      }
    }

    logger.debug(`Cleared range ${range}`);
  }

  // Chart operations
  addChart(chart: Omit<Chart, 'id'>): Chart {
    const newChart: Chart = {
      ...chart,
      id: this.generateId(),
    };

    this.charts.set(newChart.id, newChart);
    logger.debug(`Added chart: ${newChart.id}`);
    
    return newChart;
  }

  getChart(id: string): Chart | undefined {
    return this.charts.get(id);
  }

  removeChart(id: string): boolean {
    const removed = this.charts.delete(id);
    if (removed) {
      logger.debug(`Removed chart: ${id}`);
    }
    return removed;
  }

  getCharts(): Chart[] {
    return Array.from(this.charts.values());
  }

  // Named ranges
  addNamedRange(name: string, range: Range): void {
    const namedRange: NamedRange = {
      name,
      range,
      scope: 'worksheet',
    };

    this.namedRanges.set(name, namedRange);
    logger.debug(`Added named range: ${name}`);
  }

  getNamedRange(name: string): NamedRange | undefined {
    return this.namedRanges.get(name);
  }

  removeNamedRange(name: string): boolean {
    return this.namedRanges.delete(name);
  }

  getNamedRanges(): NamedRange[] {
    return Array.from(this.namedRanges.values());
  }

  // Utility methods
  private parseCellAddress(address: string | number, column?: number): CellAddress {
    if (typeof address === 'number' && typeof column === 'number') {
      return { row: address, column };
    }

    if (typeof address === 'string') {
      const match = address.match(/^([A-Z]+)(\d+)$/);
      if (match) {
        return {
          row: parseInt(match[2]),
          column: this.columnLettersToNumber(match[1]),
        };
      }
    }

    throw new Error(`Invalid cell address: ${address}`);
  }

  private parseRange(range: string): Range {
    const parts = range.split(':');
    if (parts.length !== 2) {
      throw new Error(`Invalid range format: ${range}`);
    }

    return {
      start: this.parseCellAddress(parts[0]),
      end: this.parseCellAddress(parts[1]),
    };
  }

  private getCellKey(address: CellAddress): string {
    return `${address.row},${address.column}`;
  }

  private columnLettersToNumber(letters: string): number {
    let result = 0;
    for (let i = 0; i < letters.length; i++) {
      result = result * 26 + (letters.charCodeAt(i) - 64);
    }
    return result;
  }

  private numberToColumnLetters(num: number): string {
    let result = '';
    while (num > 0) {
      num--;
      result = String.fromCharCode(65 + (num % 26)) + result;
      num = Math.floor(num / 26);
    }
    return result;
  }

  private inferType(value: any): CellValue['type'] {
    if (value === null || value === undefined) return 'string';
    if (typeof value === 'number') return 'number';
    if (typeof value === 'boolean') return 'boolean';
    if (value instanceof Date) return 'date';
    if (typeof value === 'string' && value.startsWith('=')) return 'formula';
    return 'string';
  }

  private updateMaxDimensions(address: CellAddress): void {
    this.maxRow = Math.max(this.maxRow, address.row);
    this.maxColumn = Math.max(this.maxColumn, address.column);
  }

  private generateId(): string {
    return `ws_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Serialization
  toJSON(): any {
    return {
      properties: this.properties,
      cells: Array.from(this.cells.entries()),
      charts: Array.from(this.charts.entries()),
      namedRanges: Array.from(this.namedRanges.entries()),
    };
  }

  static fromJSON(data: any): Worksheet {
    const worksheet = new Worksheet(data.properties.name);
    worksheet.properties = data.properties;
    worksheet.cells = new Map(data.cells);
    worksheet.charts = new Map(data.charts);
    worksheet.namedRanges = new Map(data.namedRanges);
    return worksheet;
  }
}
