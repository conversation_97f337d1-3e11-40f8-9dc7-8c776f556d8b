/**
 * Execution Engine - Sandboxed TypeScript execution environment
 */

import { ExecutionContext, ExecutionResult } from './types';
import { logger } from '@/utils/logger';
import { VM } from 'vm2';

export class ExecutionEngine {
  private vm: VM;
  private globalContext: any = {};

  constructor() {
    // Create sandboxed VM with limited access
    this.vm = new VM({
      timeout: 30000, // 30 second timeout
      sandbox: this.createSandbox(),
      eval: false,
      wasm: false,
    });

    logger.debug('ExecutionEngine initialized');
  }

  async execute(code: string, context: ExecutionContext): Promise<ExecutionResult> {
    const startTime = Date.now();
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Prepare execution context
      const executionCode = this.prepareCode(code, context);
      
      // Execute in sandboxed environment
      const result = this.vm.run(executionCode);
      
      const executionTime = Date.now() - startTime;
      
      logger.debug(`Code execution completed in ${executionTime}ms`);

      return {
        value: result,
        errors,
        warnings,
        executionTime,
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      logger.error('Code execution failed:', error);
      
      return {
        value: null,
        errors: [error instanceof Error ? error.message : String(error)],
        warnings,
        executionTime,
      };
    }
  }

  private createSandbox(): any {
    return {
      // Math functions
      Math,
      
      // Date functions
      Date,
      
      // Basic utilities
      console: {
        log: (...args: any[]) => logger.debug('Sandbox log:', ...args),
        warn: (...args: any[]) => logger.warn('Sandbox warn:', ...args),
        error: (...args: any[]) => logger.error('Sandbox error:', ...args),
      },
      
      // Array and Object utilities
      Array,
      Object,
      JSON,
      
      // String utilities
      String,
      Number,
      Boolean,
      
      // Regular expressions
      RegExp,
      
      // Error handling
      Error,
      
      // Assert function for validation
      assert: (condition: any, message?: string) => {
        if (!condition) {
          throw new Error(message || 'Assertion failed');
        }
      },
      
      // Utility functions for spreadsheet operations
      utils: {
        columnToNumber: (column: string): number => {
          let result = 0;
          for (let i = 0; i < column.length; i++) {
            result = result * 26 + (column.charCodeAt(i) - 64);
          }
          return result;
        },
        
        numberToColumn: (num: number): string => {
          let result = '';
          while (num > 0) {
            num--;
            result = String.fromCharCode(65 + (num % 26)) + result;
            num = Math.floor(num / 26);
          }
          return result;
        },
        
        parseAddress: (address: string) => {
          const match = address.match(/^([A-Z]+)(\d+)$/);
          if (!match) throw new Error(`Invalid address: ${address}`);
          return {
            column: match[1],
            row: parseInt(match[2]),
            columnNumber: this.columnToNumber(match[1]),
          };
        },
        
        formatCurrency: (value: number, currency = 'USD'): string => {
          return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency,
          }).format(value);
        },
        
        formatPercent: (value: number, decimals = 2): string => {
          return (value * 100).toFixed(decimals) + '%';
        },
        
        formatNumber: (value: number, decimals = 2): string => {
          return value.toFixed(decimals);
        },
      },
    };
  }

  private prepareCode(code: string, context: ExecutionContext): string {
    // Inject context variables
    let preparedCode = '';
    
    // Add workbook and worksheet to scope
    preparedCode += `const workbook = arguments[0];\n`;
    preparedCode += `const worksheet = arguments[1];\n`;
    
    // Add any additional variables
    if (context.variables) {
      for (const [name, value] of context.variables) {
        preparedCode += `const ${name} = ${JSON.stringify(value)};\n`;
      }
    }
    
    // Add the user code
    preparedCode += code;
    
    // Wrap in function to pass context
    const wrappedCode = `
      (function(workbook, worksheet) {
        ${preparedCode}
      })(arguments[0], arguments[1])
    `;
    
    return wrappedCode;
  }

  // Helper methods for common operations
  async executeFormula(formula: string, context: ExecutionContext): Promise<any> {
    // Remove leading = if present
    const cleanFormula = formula.startsWith('=') ? formula.substring(1) : formula;
    
    // Convert Excel formula to JavaScript
    const jsCode = this.convertFormulaToJS(cleanFormula);
    
    return this.execute(jsCode, context);
  }

  private convertFormulaToJS(formula: string): string {
    // Basic formula conversion (this would be much more complex in reality)
    let jsCode = formula;
    
    // Convert SUM function
    jsCode = jsCode.replace(/SUM\(([^)]+)\)/g, (match, range) => {
      return `worksheet.getRange('${range}').values.flat().reduce((sum, val) => sum + (typeof val === 'number' ? val : 0), 0)`;
    });
    
    // Convert AVERAGE function
    jsCode = jsCode.replace(/AVERAGE\(([^)]+)\)/g, (match, range) => {
      return `(() => {
        const values = worksheet.getRange('${range}').values.flat().filter(val => typeof val === 'number');
        return values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0;
      })()`;
    });
    
    // Convert COUNT function
    jsCode = jsCode.replace(/COUNT\(([^)]+)\)/g, (match, range) => {
      return `worksheet.getRange('${range}').values.flat().filter(val => typeof val === 'number').length`;
    });
    
    // Convert MAX function
    jsCode = jsCode.replace(/MAX\(([^)]+)\)/g, (match, range) => {
      return `Math.max(...worksheet.getRange('${range}').values.flat().filter(val => typeof val === 'number'))`;
    });
    
    // Convert MIN function
    jsCode = jsCode.replace(/MIN\(([^)]+)\)/g, (match, range) => {
      return `Math.min(...worksheet.getRange('${range}').values.flat().filter(val => typeof val === 'number'))`;
    });
    
    // Convert cell references (A1, B2, etc.)
    jsCode = jsCode.replace(/([A-Z]+\d+)/g, (match) => {
      return `worksheet.getCell('${match}').value`;
    });
    
    return jsCode;
  }

  // Security and validation
  private validateCode(code: string): string[] {
    const warnings: string[] = [];
    
    // Check for potentially dangerous operations
    const dangerousPatterns = [
      /require\s*\(/,
      /import\s+/,
      /eval\s*\(/,
      /Function\s*\(/,
      /process\./,
      /global\./,
      /__dirname/,
      /__filename/,
    ];
    
    for (const pattern of dangerousPatterns) {
      if (pattern.test(code)) {
        warnings.push(`Potentially unsafe operation detected: ${pattern.source}`);
      }
    }
    
    return warnings;
  }

  // Memory and performance monitoring
  getMemoryUsage(): number {
    if (typeof process !== 'undefined' && process.memoryUsage) {
      return process.memoryUsage().heapUsed;
    }
    return 0;
  }

  // Cleanup
  dispose(): void {
    // Clean up VM resources
    logger.debug('ExecutionEngine disposed');
  }
}
