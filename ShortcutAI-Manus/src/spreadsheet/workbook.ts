/**
 * Workbook implementation and manager for the spreadsheet system
 */

import { Worksheet } from './worksheet';
import { 
  WorkbookOptions, WorkbookProperties, NamedRange, Checkpoint,
  SpreadsheetEvent, WorksheetEvent, WorkbookEvent
} from './types';
import { logger } from '@/utils/logger';
import { EventEmitter } from 'events';

export class Workbook extends EventEmitter {
  private properties: WorkbookProperties;
  private worksheets: Map<string, Worksheet> = new Map();
  private worksheetOrder: string[] = [];
  private activeWorksheetId?: string;
  private namedRanges: Map<string, NamedRange> = new Map();
  private checkpoints: Map<string, Checkpoint> = new Map();

  constructor(options?: WorkbookOptions) {
    super();
    
    this.properties = {
      id: this.generateId(),
      title: options?.title,
      author: options?.author,
      created: new Date(),
      modified: new Date(),
      worksheetCount: 0,
      namedRanges: [],
    };

    // Create default worksheet
    this.addWorksheet('Sheet1');
    
    logger.debug(`Created workbook: ${this.properties.id}`);
  }

  // Properties
  get id(): string { return this.properties.id; }
  get title(): string | undefined { return this.properties.title; }
  get author(): string | undefined { return this.properties.author; }
  get created(): Date { return this.properties.created; }
  get modified(): Date { return this.properties.modified; }
  get worksheetCount(): number { return this.worksheets.size; }

  // Worksheet operations
  addWorksheet(name: string, options?: any): Worksheet {
    // Ensure unique name
    let uniqueName = name;
    let counter = 1;
    while (this.getWorksheetByName(uniqueName)) {
      uniqueName = `${name}${counter}`;
      counter++;
    }

    const worksheet = new Worksheet(uniqueName, options);
    worksheet.properties.index = this.worksheets.size;
    
    this.worksheets.set(worksheet.id, worksheet);
    this.worksheetOrder.push(worksheet.id);
    
    // Set as active if it's the first worksheet
    if (!this.activeWorksheetId) {
      this.activeWorksheetId = worksheet.id;
    }

    // Listen to worksheet events
    worksheet.on('cell_change', (event) => {
      this.emit('cell_change', event);
    });

    // Emit worksheet added event
    const event: WorksheetEvent = {
      type: 'worksheet_added',
      worksheetId: worksheet.id,
      worksheetName: worksheet.name,
      timestamp: new Date(),
    };
    this.emit('worksheet_added', event);

    this.updateModified();
    logger.debug(`Added worksheet: ${uniqueName}`);
    
    return worksheet;
  }

  getWorksheet(id: string): Worksheet | undefined {
    return this.worksheets.get(id);
  }

  getWorksheetByName(name: string): Worksheet | undefined {
    for (const worksheet of this.worksheets.values()) {
      if (worksheet.name === name) {
        return worksheet;
      }
    }
    return undefined;
  }

  getActiveWorksheet(): Worksheet | undefined {
    return this.activeWorksheetId ? this.worksheets.get(this.activeWorksheetId) : undefined;
  }

  setActiveWorksheet(id: string): boolean {
    if (!this.worksheets.has(id)) {
      return false;
    }
    
    this.activeWorksheetId = id;
    logger.debug(`Set active worksheet: ${id}`);
    return true;
  }

  getWorksheets(): Worksheet[] {
    return this.worksheetOrder.map(id => this.worksheets.get(id)!);
  }

  // Checkpoint management
  createCheckpoint(description?: string): string {
    const checkpoint: Checkpoint = {
      id: this.generateId(),
      timestamp: new Date(),
      description: description || `Checkpoint at ${new Date().toISOString()}`,
      data: this.serialize(),
    };

    this.checkpoints.set(checkpoint.id, checkpoint);
    logger.debug(`Created checkpoint: ${checkpoint.id}`);
    
    return checkpoint.id;
  }

  restoreCheckpoint(checkpointId: string): boolean {
    const checkpoint = this.checkpoints.get(checkpointId);
    if (!checkpoint) {
      return false;
    }

    try {
      this.deserialize(checkpoint.data);
      logger.debug(`Restored checkpoint: ${checkpointId}`);
      return true;
    } catch (error) {
      logger.error(`Failed to restore checkpoint: ${checkpointId}`, error);
      return false;
    }
  }

  getCheckpoints(): Checkpoint[] {
    return Array.from(this.checkpoints.values()).sort((a, b) => 
      b.timestamp.getTime() - a.timestamp.getTime()
    );
  }

  removeCheckpoint(checkpointId: string): boolean {
    return this.checkpoints.delete(checkpointId);
  }

  // Utility methods
  private updateModified(): void {
    this.properties.modified = new Date();
  }

  private updateWorksheetIndices(): void {
    this.worksheetOrder.forEach((id, index) => {
      const worksheet = this.worksheets.get(id);
      if (worksheet) {
        worksheet.properties.index = index;
      }
    });
  }

  private generateId(): string {
    return `wb_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Serialization
  serialize(): any {
    return {
      properties: this.properties,
      worksheets: Array.from(this.worksheets.entries()).map(([id, ws]) => [id, ws.toJSON()]),
      worksheetOrder: this.worksheetOrder,
      activeWorksheetId: this.activeWorksheetId,
      namedRanges: Array.from(this.namedRanges.entries()),
    };
  }

  deserialize(data: any): void {
    this.properties = data.properties;
    this.worksheets.clear();
    this.worksheetOrder = data.worksheetOrder;
    this.activeWorksheetId = data.activeWorksheetId;
    this.namedRanges = new Map(data.namedRanges);

    // Restore worksheets
    for (const [id, wsData] of data.worksheets) {
      const worksheet = Worksheet.fromJSON(wsData);
      this.worksheets.set(id, worksheet);
      
      // Re-attach event listeners
      worksheet.on('cell_change', (event) => {
        this.emit('cell_change', event);
      });
    }
  }

  toJSON(): any {
    return this.serialize();
  }

  static fromJSON(data: any): Workbook {
    const workbook = new Workbook();
    workbook.deserialize(data);
    return workbook;
  }
}

export class WorkbookManager {
  private workbooks: Map<string, Workbook> = new Map();
  private activeWorkbookId?: string;

  async initialize(): Promise<void> {
    logger.info('WorkbookManager initialized');
  }

  createWorkbook(options?: WorkbookOptions): Workbook {
    const workbook = new Workbook(options);
    this.workbooks.set(workbook.id, workbook);
    
    if (!this.activeWorkbookId) {
      this.activeWorkbookId = workbook.id;
    }

    logger.debug(`Created workbook: ${workbook.id}`);
    return workbook;
  }

  getWorkbook(id: string): Workbook | undefined {
    return this.workbooks.get(id);
  }

  getActiveWorkbook(): Workbook | undefined {
    return this.activeWorkbookId ? this.workbooks.get(this.activeWorkbookId) : undefined;
  }

  setActiveWorkbook(id: string): boolean {
    if (!this.workbooks.has(id)) {
      return false;
    }
    
    this.activeWorkbookId = id;
    return true;
  }

  removeWorkbook(id: string): boolean {
    const removed = this.workbooks.delete(id);
    
    if (removed && this.activeWorkbookId === id) {
      // Set new active workbook
      const workbookIds = Array.from(this.workbooks.keys());
      this.activeWorkbookId = workbookIds.length > 0 ? workbookIds[0] : undefined;
    }

    return removed;
  }

  getWorkbooks(): Workbook[] {
    return Array.from(this.workbooks.values());
  }

  async createCheckpoint(workbookId?: string): Promise<string> {
    const workbook = workbookId ? this.getWorkbook(workbookId) : this.getActiveWorkbook();
    if (!workbook) {
      throw new Error('No workbook available for checkpoint');
    }

    return workbook.createCheckpoint();
  }

  async cleanup(): Promise<void> {
    this.workbooks.clear();
    this.activeWorkbookId = undefined;
    logger.info('WorkbookManager cleaned up');
  }
}
