/**
 * Privacy and Security Manager - Handles data isolation and temporary processing
 */

import { SecurityContext, Permission } from '@/spreadsheet/types';
import { logger } from '@/utils/logger';
import { randomUUID } from 'crypto';

export interface SessionData {
  id: string;
  userId?: string;
  createdAt: Date;
  lastActivity: Date;
  permissions: Permission[];
  workbookIds: string[];
  temporary: boolean;
}

export class SecurityManager {
  private sessions: Map<string, SessionData> = new Map();
  private sessionTimeout = 3600000; // 1 hour
  private cleanupInterval?: NodeJS.Timeout;

  async initialize(): Promise<void> {
    // Start cleanup interval
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredSessions();
    }, 300000); // Check every 5 minutes

    logger.info('SecurityManager initialized');
  }

  async createSession(userId?: string, temporary = true): Promise<string> {
    const sessionId = randomUUID();
    
    const session: SessionData = {
      id: sessionId,
      userId,
      createdAt: new Date(),
      lastActivity: new Date(),
      permissions: this.getDefaultPermissions(),
      workbookIds: [],
      temporary,
    };

    this.sessions.set(sessionId, session);
    
    logger.debug(`Created session: ${sessionId} (temporary: ${temporary})`);
    return sessionId;
  }

  async getSession(sessionId: string): Promise<SessionData | undefined> {
    const session = this.sessions.get(sessionId);
    
    if (session) {
      // Update last activity
      session.lastActivity = new Date();
      this.sessions.set(sessionId, session);
    }

    return session;
  }

  async endSession(sessionId: string): Promise<boolean> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return false;
    }

    // Clean up session data
    if (session.temporary) {
      await this.cleanupSessionData(session);
    }

    this.sessions.delete(sessionId);
    logger.debug(`Ended session: ${sessionId}`);
    
    return true;
  }

  async validatePermission(sessionId: string, permission: Permission): Promise<boolean> {
    const session = await this.getSession(sessionId);
    if (!session) {
      return false;
    }

    // Check if session has the required permission
    return session.permissions.some(p => 
      p.type === permission.type && 
      p.scope === permission.scope &&
      (!permission.target || p.target === permission.target)
    );
  }

  async addWorkbookToSession(sessionId: string, workbookId: string): Promise<boolean> {
    const session = await this.getSession(sessionId);
    if (!session) {
      return false;
    }

    if (!session.workbookIds.includes(workbookId)) {
      session.workbookIds.push(workbookId);
      this.sessions.set(sessionId, session);
    }

    return true;
  }

  async removeWorkbookFromSession(sessionId: string, workbookId: string): Promise<boolean> {
    const session = await this.getSession(sessionId);
    if (!session) {
      return false;
    }

    const index = session.workbookIds.indexOf(workbookId);
    if (index > -1) {
      session.workbookIds.splice(index, 1);
      this.sessions.set(sessionId, session);
    }

    return true;
  }

  private getDefaultPermissions(): Permission[] {
    return [
      { type: 'read', scope: 'workbook' },
      { type: 'write', scope: 'workbook' },
      { type: 'execute', scope: 'workbook' },
    ];
  }

  private async cleanupSessionData(session: SessionData): Promise<void> {
    // In a real implementation, this would clean up:
    // - Temporary files
    // - Cached data
    // - Workbook data
    // - Any other session-specific resources
    
    logger.debug(`Cleaning up data for session: ${session.id}`);
    
    // Simulate cleanup
    for (const workbookId of session.workbookIds) {
      logger.debug(`Cleaning up workbook: ${workbookId}`);
    }
  }

  private cleanupExpiredSessions(): void {
    const now = new Date();
    const expiredSessions: string[] = [];

    for (const [sessionId, session] of this.sessions) {
      const timeSinceActivity = now.getTime() - session.lastActivity.getTime();
      
      if (timeSinceActivity > this.sessionTimeout) {
        expiredSessions.push(sessionId);
      }
    }

    // Clean up expired sessions
    for (const sessionId of expiredSessions) {
      this.endSession(sessionId).catch(error => {
        logger.error(`Failed to cleanup expired session ${sessionId}:`, error);
      });
    }

    if (expiredSessions.length > 0) {
      logger.debug(`Cleaned up ${expiredSessions.length} expired sessions`);
    }
  }

  // Data encryption/decryption (placeholder)
  async encryptData(data: any): Promise<string> {
    // In a real implementation, this would use proper encryption
    return Buffer.from(JSON.stringify(data)).toString('base64');
  }

  async decryptData(encryptedData: string): Promise<any> {
    // In a real implementation, this would use proper decryption
    return JSON.parse(Buffer.from(encryptedData, 'base64').toString());
  }

  // Audit logging
  async logSecurityEvent(event: {
    type: 'session_created' | 'session_ended' | 'permission_denied' | 'data_access';
    sessionId: string;
    details?: any;
  }): Promise<void> {
    logger.info('Security event:', {
      ...event,
      timestamp: new Date().toISOString(),
    });
  }

  // Data anonymization
  async anonymizeData(data: any): Promise<any> {
    // Simple anonymization - in reality this would be more sophisticated
    if (typeof data === 'object' && data !== null) {
      const anonymized = { ...data };
      
      // Remove or hash sensitive fields
      if (anonymized.email) {
        anonymized.email = this.hashString(anonymized.email);
      }
      if (anonymized.name) {
        anonymized.name = 'Anonymous User';
      }
      if (anonymized.phone) {
        anonymized.phone = 'XXX-XXX-XXXX';
      }
      
      return anonymized;
    }
    
    return data;
  }

  private hashString(input: string): string {
    // Simple hash - in reality would use crypto.createHash
    let hash = 0;
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return `hash_${Math.abs(hash)}`;
  }

  // Compliance and data retention
  async deleteUserData(userId: string): Promise<boolean> {
    let deletedSessions = 0;
    
    for (const [sessionId, session] of this.sessions) {
      if (session.userId === userId) {
        await this.endSession(sessionId);
        deletedSessions++;
      }
    }

    logger.info(`Deleted data for user ${userId}: ${deletedSessions} sessions`);
    return true;
  }

  async exportUserData(userId: string): Promise<any> {
    const userSessions = Array.from(this.sessions.values())
      .filter(session => session.userId === userId);

    return {
      userId,
      sessions: userSessions.map(session => ({
        id: session.id,
        createdAt: session.createdAt,
        lastActivity: session.lastActivity,
        workbookCount: session.workbookIds.length,
      })),
      exportedAt: new Date().toISOString(),
    };
  }

  async cleanup(): Promise<void> {
    // Clear cleanup interval
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    // End all sessions
    const sessionIds = Array.from(this.sessions.keys());
    for (const sessionId of sessionIds) {
      await this.endSession(sessionId);
    }

    logger.info('SecurityManager cleaned up');
  }

  // Statistics and monitoring
  getSessionStats(): {
    totalSessions: number;
    activeSessions: number;
    temporarySessions: number;
    averageSessionDuration: number;
  } {
    const now = new Date();
    const sessions = Array.from(this.sessions.values());
    
    const activeSessions = sessions.filter(session => {
      const timeSinceActivity = now.getTime() - session.lastActivity.getTime();
      return timeSinceActivity < this.sessionTimeout;
    });

    const temporarySessions = sessions.filter(session => session.temporary);
    
    const totalDuration = sessions.reduce((total, session) => {
      return total + (session.lastActivity.getTime() - session.createdAt.getTime());
    }, 0);
    
    const averageSessionDuration = sessions.length > 0 ? totalDuration / sessions.length : 0;

    return {
      totalSessions: sessions.length,
      activeSessions: activeSessions.length,
      temporarySessions: temporarySessions.length,
      averageSessionDuration,
    };
  }
}
