/**
 * Authentication Manager - Handles user authentication and authorization
 */

import { logger } from '@/utils/logger';
import { randomUUID } from 'crypto';

export interface User {
  id: string;
  email: string;
  name: string;
  role: 'user' | 'admin' | 'enterprise';
  permissions: string[];
  createdAt: Date;
  lastLogin?: Date;
  active: boolean;
}

export interface AuthToken {
  token: string;
  userId: string;
  expiresAt: Date;
  type: 'access' | 'refresh';
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface LoginResult {
  success: boolean;
  user?: User;
  accessToken?: string;
  refreshToken?: string;
  error?: string;
}

export class AuthManager {
  private users: Map<string, User> = new Map();
  private tokens: Map<string, AuthToken> = new Map();
  private tokenExpiry = 3600000; // 1 hour for access tokens
  private refreshTokenExpiry = 604800000; // 7 days for refresh tokens

  async initialize(): Promise<void> {
    // Create default admin user for demo
    await this.createUser({
      email: '<EMAIL>',
      name: 'Admin User',
      role: 'admin',
      password: 'admin123', // In reality, this would be properly hashed
    });

    logger.info('AuthManager initialized');
  }

  async createUser(userData: {
    email: string;
    name: string;
    role: 'user' | 'admin' | 'enterprise';
    password: string;
  }): Promise<User> {
    const userId = randomUUID();
    
    const user: User = {
      id: userId,
      email: userData.email,
      name: userData.name,
      role: userData.role,
      permissions: this.getDefaultPermissions(userData.role),
      createdAt: new Date(),
      active: true,
    };

    this.users.set(userId, user);
    
    // In a real implementation, password would be properly hashed and stored
    logger.debug(`Created user: ${userData.email}`);
    
    return user;
  }

  async login(credentials: LoginCredentials): Promise<LoginResult> {
    try {
      // Find user by email
      const user = Array.from(this.users.values())
        .find(u => u.email === credentials.email && u.active);

      if (!user) {
        return {
          success: false,
          error: 'Invalid credentials',
        };
      }

      // In a real implementation, password would be properly verified
      // For demo purposes, we'll accept any password for the admin user
      if (user.email === '<EMAIL>' || credentials.password === 'demo') {
        // Update last login
        user.lastLogin = new Date();
        this.users.set(user.id, user);

        // Generate tokens
        const accessToken = await this.generateToken(user.id, 'access');
        const refreshToken = await this.generateToken(user.id, 'refresh');

        logger.info(`User logged in: ${user.email}`);

        return {
          success: true,
          user,
          accessToken,
          refreshToken,
        };
      }

      return {
        success: false,
        error: 'Invalid credentials',
      };

    } catch (error) {
      logger.error('Login failed:', error);
      return {
        success: false,
        error: 'Login failed',
      };
    }
  }

  async logout(token: string): Promise<boolean> {
    const authToken = this.tokens.get(token);
    if (!authToken) {
      return false;
    }

    // Remove token
    this.tokens.delete(token);
    
    // Remove associated refresh token if this is an access token
    if (authToken.type === 'access') {
      const refreshTokens = Array.from(this.tokens.entries())
        .filter(([_, t]) => t.userId === authToken.userId && t.type === 'refresh');
      
      for (const [refreshToken] of refreshTokens) {
        this.tokens.delete(refreshToken);
      }
    }

    logger.debug(`User logged out: ${authToken.userId}`);
    return true;
  }

  async validateToken(token: string): Promise<User | null> {
    const authToken = this.tokens.get(token);
    if (!authToken) {
      return null;
    }

    // Check if token is expired
    if (new Date() > authToken.expiresAt) {
      this.tokens.delete(token);
      return null;
    }

    // Get user
    const user = this.users.get(authToken.userId);
    if (!user || !user.active) {
      return null;
    }

    return user;
  }

  async refreshToken(refreshToken: string): Promise<{ accessToken?: string; error?: string }> {
    const authToken = this.tokens.get(refreshToken);
    if (!authToken || authToken.type !== 'refresh') {
      return { error: 'Invalid refresh token' };
    }

    // Check if refresh token is expired
    if (new Date() > authToken.expiresAt) {
      this.tokens.delete(refreshToken);
      return { error: 'Refresh token expired' };
    }

    // Generate new access token
    const accessToken = await this.generateToken(authToken.userId, 'access');
    
    logger.debug(`Token refreshed for user: ${authToken.userId}`);
    
    return { accessToken };
  }

  async getUserById(userId: string): Promise<User | undefined> {
    return this.users.get(userId);
  }

  async updateUser(userId: string, updates: Partial<User>): Promise<boolean> {
    const user = this.users.get(userId);
    if (!user) {
      return false;
    }

    const updatedUser = { ...user, ...updates };
    this.users.set(userId, updatedUser);
    
    logger.debug(`Updated user: ${userId}`);
    return true;
  }

  async deactivateUser(userId: string): Promise<boolean> {
    const user = this.users.get(userId);
    if (!user) {
      return false;
    }

    user.active = false;
    this.users.set(userId, user);

    // Invalidate all tokens for this user
    const userTokens = Array.from(this.tokens.entries())
      .filter(([_, token]) => token.userId === userId);
    
    for (const [tokenString] of userTokens) {
      this.tokens.delete(tokenString);
    }

    logger.info(`Deactivated user: ${user.email}`);
    return true;
  }

  private async generateToken(userId: string, type: 'access' | 'refresh'): Promise<string> {
    const token = randomUUID();
    const expiresAt = new Date();
    
    if (type === 'access') {
      expiresAt.setTime(expiresAt.getTime() + this.tokenExpiry);
    } else {
      expiresAt.setTime(expiresAt.getTime() + this.refreshTokenExpiry);
    }

    const authToken: AuthToken = {
      token,
      userId,
      expiresAt,
      type,
    };

    this.tokens.set(token, authToken);
    return token;
  }

  private getDefaultPermissions(role: 'user' | 'admin' | 'enterprise'): string[] {
    switch (role) {
      case 'admin':
        return [
          'read:all',
          'write:all',
          'execute:all',
          'admin:users',
          'admin:system',
        ];
      case 'enterprise':
        return [
          'read:workbook',
          'write:workbook',
          'execute:workbook',
          'share:workbook',
          'export:workbook',
        ];
      case 'user':
      default:
        return [
          'read:workbook',
          'write:workbook',
          'execute:workbook',
        ];
    }
  }

  // Token cleanup
  private cleanupExpiredTokens(): void {
    const now = new Date();
    const expiredTokens: string[] = [];

    for (const [tokenString, token] of this.tokens) {
      if (now > token.expiresAt) {
        expiredTokens.push(tokenString);
      }
    }

    for (const tokenString of expiredTokens) {
      this.tokens.delete(tokenString);
    }

    if (expiredTokens.length > 0) {
      logger.debug(`Cleaned up ${expiredTokens.length} expired tokens`);
    }
  }

  // Statistics
  getAuthStats(): {
    totalUsers: number;
    activeUsers: number;
    activeTokens: number;
    usersByRole: Record<string, number>;
  } {
    const users = Array.from(this.users.values());
    const activeUsers = users.filter(u => u.active);
    const now = new Date();
    const activeTokens = Array.from(this.tokens.values())
      .filter(t => now <= t.expiresAt);

    const usersByRole = users.reduce((acc, user) => {
      acc[user.role] = (acc[user.role] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalUsers: users.length,
      activeUsers: activeUsers.length,
      activeTokens: activeTokens.length,
      usersByRole,
    };
  }

  async cleanup(): Promise<void> {
    this.users.clear();
    this.tokens.clear();
    logger.info('AuthManager cleaned up');
  }
}
